Option Explicit

'==============================================================================
' 快速诊断脚本
' 专门诊断为什么无法进行T3->T4合成
'==============================================================================

' 快速诊断T3->T4问题
Public Sub QuickDiagnoseT3T4()
    On Error GoTo ErrorHandler
    
    ' 初始化系统
    Call InitializeOptimizedSystem
    
    Dim diagnosis As String
    diagnosis = "=== T3->T4合成问题快速诊断 ===" & vbCrLf & vbCrLf
    
    ' 1. 检查T3->T4规则是否存在
    diagnosis = diagnosis & "1. 检查T3->T4合成规则:" & vbCrLf
    
    Dim t3t4Rules As Variant
    t3t4Rules = Array("物质 + 能量", "精神 + 虚空", "物质 + 精神", "精神 + 能量", "虚空 + 物质", "能量 + 虚空")
    
    Dim ruleCount As Integer
    ruleCount = 0
    
    Dim i As Integer
    For i = LBound(t3t4Rules) To UBound(t3t4Rules)
        If g_OptimizedRules.Exists(t3t4Rules(i)) Then
            Dim ruleInfo As Variant
            ruleInfo = g_OptimizedRules(t3t4Rules(i))
            diagnosis = diagnosis & "   ✅ " & t3t4Rules(i) & " -> " & ruleInfo(0) & vbCrLf
            ruleCount = ruleCount + 1
        Else
            diagnosis = diagnosis & "   ❌ " & t3t4Rules(i) & " - 缺失" & vbCrLf
        End If
    Next i
    
    diagnosis = diagnosis & "   T3->T4规则总数: " & ruleCount & "/6" & vbCrLf & vbCrLf
    
    ' 2. 模拟T3魔法池状态
    diagnosis = diagnosis & "2. 模拟T3阶段魔法池:" & vbCrLf
    
    Dim testT3Pool As Object
    Set testT3Pool = CreateObject("Scripting.Dictionary")
    testT3Pool.Add "物质1", 1
    testT3Pool.Add "能量1", 1
    testT3Pool.Add "精神1", 1
    testT3Pool.Add "虚空1", 1
    
    diagnosis = diagnosis & "   测试魔法池: 物质1, 能量1, 精神1, 虚空1" & vbCrLf
    
    ' 3. 测试合成查找
    Dim bestSynthesis As Variant
    bestSynthesis = FindOptimizedBestSynthesis(testT3Pool)
    
    If IsEmpty(bestSynthesis) Then
        diagnosis = diagnosis & "   ❌ 无法找到合成选项！" & vbCrLf
        diagnosis = diagnosis & vbCrLf & "3. 详细问题分析:" & vbCrLf
        
        ' 手动检查每个可能的组合
        Dim combos As Variant
        combos = Array( _
            Array("物质1", "能量1", "物质 + 能量"), _
            Array("精神1", "虚空1", "精神 + 虚空"), _
            Array("物质1", "精神1", "物质 + 精神") _
        )
        
        Dim j As Integer
        For j = LBound(combos) To UBound(combos)
            Dim magic1 As String, magic2 As String, expectedRule As String
            magic1 = combos(j)(0)
            magic2 = combos(j)(1)
            expectedRule = combos(j)(2)
            
            diagnosis = diagnosis & "   测试组合: " & magic1 & " + " & magic2 & vbCrLf
            
            ' 检查魔法标签提取
            Dim tag1 As String, tag2 As String
            tag1 = GetOptimizedMagicTag(magic1)
            tag2 = GetOptimizedMagicTag(magic2)
            
            diagnosis = diagnosis & "     标签提取: " & tag1 & " + " & tag2 & vbCrLf
            
            ' 检查规则匹配
            Dim comboKey As String
            comboKey = tag1 & " + " & tag2
            
            If g_OptimizedRules.Exists(comboKey) Then
                diagnosis = diagnosis & "     ✅ 规则存在: " & comboKey & vbCrLf
            Else
                diagnosis = diagnosis & "     ❌ 规则不存在: " & comboKey & vbCrLf
            End If
        Next j
        
    Else
        diagnosis = diagnosis & "   ✅ 找到合成选项: " & bestSynthesis(0) & " + " & bestSynthesis(1) & " -> " & bestSynthesis(2) & vbCrLf
    End If
    
    MsgBox diagnosis, vbInformation, "T3->T4诊断结果"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "诊断过程中发生错误: " & Err.Description, vbCritical
End Sub

' 模拟真实的合成过程
Public Sub SimulateRealProcess()
    On Error GoTo ErrorHandler
    
    ' 初始化系统
    Call InitializeOptimizedSystem
    
    ' 读取真实魔法池
    Dim playerPool As Object
    Set playerPool = ReadMagicPoolFromExcel()
    
    If playerPool.Count = 0 Then
        MsgBox "魔法池为空！", vbExclamation
        Exit Sub
    End If
    
    MsgBox "开始模拟真实合成过程..." & vbCrLf & vbCrLf & _
           "初始魔法池: " & GetPoolSummary(playerPool), vbInformation
    
    ' 执行前几步合成，观察T3阶段
    Dim step As Integer
    step = 0
    
    Do While step < 30 ' 限制步数，避免无限循环
        step = step + 1
        
        ' 检查当前最高等级
        Dim maxTier As Integer
        maxTier = GetMaxTierInPool(playerPool)
        
        ' 寻找合成选项
        Dim bestSynthesis As Variant
        bestSynthesis = FindOptimizedBestSynthesis(playerPool)
        
        If IsEmpty(bestSynthesis) Then
            MsgBox "第" & step & "步: 无法找到合成选项" & vbCrLf & vbCrLf & _
                   "当前魔法池: " & GetPoolSummary(playerPool) & vbCrLf & _
                   "最高等级: T" & maxTier, vbExclamation
            Exit Do
        End If
        
        ' 如果到达T3阶段，详细记录
        If maxTier >= 3 Then
            Dim detailMsg As String
            detailMsg = "第" & step & "步 (T3阶段):" & vbCrLf & vbCrLf & _
                       "当前魔法池: " & GetPoolSummary(playerPool) & vbCrLf & vbCrLf & _
                       "选择合成: " & bestSynthesis(0) & " + " & bestSynthesis(1) & " -> " & bestSynthesis(2) & vbCrLf & _
                       "合成类型: " & bestSynthesis(3) & vbCrLf & vbCrLf & _
                       "继续？"
            
            If MsgBox(detailMsg, vbYesNo + vbQuestion) = vbNo Then
                Exit Do
            End If
        End If
        
        ' 执行合成
        Dim dummyStats As Object
        Set dummyStats = CreateObject("Scripting.Dictionary")
        Call ExecuteOptimizedSynthesis(playerPool, bestSynthesis, dummyStats)
        
        ' 如果获得T6，结束
        If HasOptimizedMagicOfTier(playerPool, 6) Then
            MsgBox "第" & step & "步: 成功获得T6魔法！", vbInformation
            Exit Do
        End If
    Loop
    
    MsgBox "模拟结束。最终魔法池: " & GetPoolSummary(playerPool), vbInformation
    
    Exit Sub
    
ErrorHandler:
    MsgBox "模拟过程中发生错误: " & Err.Description, vbCritical
End Sub

' 获取魔法池中的最高等级
Private Function GetMaxTierInPool(playerPool As Object) As Integer
    Dim maxTier As Integer
    maxTier = 0
    
    Dim key As Variant
    For Each key In playerPool.Keys
        Dim tier As Integer
        tier = GetOptimizedMagicTier(CStr(key))
        If tier > maxTier Then
            maxTier = tier
        End If
    Next key
    
    GetMaxTierInPool = maxTier
End Function

' 获取魔法池摘要
Private Function GetPoolSummary(playerPool As Object) As String
    Dim summary As String
    summary = ""
    
    Dim tierCounts(1 To 6) As Long
    Dim key As Variant
    For Each key In playerPool.Keys
        Dim tier As Integer
        tier = GetOptimizedMagicTier(CStr(key))
        If tier >= 1 And tier <= 6 Then
            tierCounts(tier) = tierCounts(tier) + playerPool(key)
        End If
    Next key
    
    Dim i As Integer
    For i = 1 To 6
        If tierCounts(i) > 0 Then
            If summary <> "" Then summary = summary & ", "
            summary = summary & "T" & i & ":" & tierCounts(i) & "个"
        End If
    Next i
    
    If summary = "" Then summary = "空"
    GetPoolSummary = summary
End Function

' 检查优先级计算
Public Sub CheckPriorityCalculation()
    On Error GoTo ErrorHandler
    
    ' 初始化系统
    Call InitializeOptimizedSystem
    
    Dim report As String
    report = "=== 优先级计算检查 ===" & vbCrLf & vbCrLf
    
    ' 检查优先级设置
    Dim tierPriority As Object
    Set tierPriority = CreateObject("Scripting.Dictionary")
    tierPriority.Add "Tier 5 -> Tier 6", 5
    tierPriority.Add "Tier 4 -> Tier 5", 4
    tierPriority.Add "Tier 3 -> Tier 4", 3
    tierPriority.Add "Tier 2 -> Tier 3", 2
    tierPriority.Add "Tier 1 -> Tier 2", 1
    
    report = report & "优先级设置:" & vbCrLf
    Dim key As Variant
    For Each key In tierPriority.Keys
        report = report & "  " & key & ": " & tierPriority(key) & vbCrLf
    Next key
    
    report = report & vbCrLf & "检查实际规则的优先级匹配:" & vbCrLf
    
    ' 检查一些关键规则的优先级
    Dim testRules As Variant
    testRules = Array("物质 + 能量", "因果 + 平衡", "存在 + 非在")
    
    Dim i As Integer
    For i = LBound(testRules) To UBound(testRules)
        Dim ruleName As String
        ruleName = testRules(i)
        
        If g_OptimizedRules.Exists(ruleName) Then
            Dim ruleInfo As Variant
            ruleInfo = g_OptimizedRules(ruleName)
            
            Dim tierTransition As String
            tierTransition = ruleInfo(1)
            
            Dim priority As Integer
            If tierPriority.Exists(tierTransition) Then
                priority = tierPriority(tierTransition)
                report = report & "  ✅ " & ruleName & " -> " & ruleInfo(0) & " (优先级: " & priority & ")" & vbCrLf
            Else
                report = report & "  ❌ " & ruleName & " -> " & ruleInfo(0) & " (优先级匹配失败: " & tierTransition & ")" & vbCrLf
            End If
        Else
            report = report & "  ❌ " & ruleName & " - 规则不存在" & vbCrLf
        End If
    Next i
    
    MsgBox report, vbInformation, "优先级检查"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "检查优先级时发生错误: " & Err.Description, vbCritical
End Sub

' 显示诊断菜单
Public Sub ShowDiagnosisMenu()
    Dim choice As String
    choice = InputBox( _
        "=== 快速诊断菜单 ===" & vbCrLf & vbCrLf & _
        "1 - 快速诊断T3->T4问题" & vbCrLf & _
        "2 - 模拟真实合成过程" & vbCrLf & _
        "3 - 检查优先级计算" & vbCrLf & vbCrLf & _
        "请输入选项编号:", _
        "诊断工具", "1")
    
    Select Case choice
        Case "1"
            Call QuickDiagnoseT3T4
        Case "2"
            Call SimulateRealProcess
        Case "3"
            Call CheckPriorityCalculation
        Case Else
            If choice <> "" Then
                MsgBox "无效的选项！", vbExclamation
            End If
    End Select
End Sub
