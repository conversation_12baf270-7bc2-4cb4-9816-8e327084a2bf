Option Explicit

'==============================================================================
' 支持同标签合成的模拟器
' 修改原有逻辑，允许相同标签的魔法进行合成
'==============================================================================

' 全局变量
Public g_SameTagRules As Object
Public g_SameTagTierMap As Object

' 初始化支持同标签合成的系统
Public Sub InitializeSameTagSystem()
    Set g_SameTagRules = CreateObject("Scripting.Dictionary")
    Set g_SameTagTierMap = CreateObject("Scripting.Dictionary")
    
    ' 加载原有的异标签合成规则
    Call LoadOriginalRules
    
    ' 加载同标签合成规则
    Call LoadSameTagRules
    
    ' 初始化魔法等级映射
    Call InitializeSameTagTierMap
End Sub

' 加载原有的异标签合成规则
Private Sub LoadOriginalRules()
    On Error GoTo ErrorHandler
    
    Dim wsRules As Worksheet
    Set wsRules = ThisWorkbook.Sheets("合成规则")
    
    Dim lastRow As Long
    lastRow = wsRules.Cells(wsRules.Rows.Count, 1).End(xlUp).Row
    
    Dim i As Long
    For i = 2 To lastRow
        Dim comboKey As String
        Dim resultMagic As String
        Dim tierTransition As String
        
        comboKey = Trim(wsRules.Cells(i, 1).Value)
        resultMagic = Trim(wsRules.Cells(i, 2).Value)
        tierTransition = Trim(wsRules.Cells(i, 3).Value)
        
        If comboKey <> "" And resultMagic <> "" Then
            ' 修正结果魔法名称格式
            Dim correctedResultMagic As String
            correctedResultMagic = resultMagic
            
            If Len(resultMagic) > 0 Then
                Dim lastChar As String
                lastChar = Right(resultMagic, 1)
                If Not IsNumeric(lastChar) Then
                    correctedResultMagic = resultMagic & "1"
                End If
            End If
            
            ' 存储合成规则
            g_SameTagRules.Add comboKey, Array(correctedResultMagic, tierTransition)
            
            ' 同时存储反向组合
            Dim reversedKey As String
            reversedKey = GetReversedComboKey(comboKey)
            If reversedKey <> comboKey And Not g_SameTagRules.Exists(reversedKey) Then
                g_SameTagRules.Add reversedKey, Array(correctedResultMagic, tierTransition)
            End If
        End If
    Next i
    
    Exit Sub
    
ErrorHandler:
    MsgBox "加载原有规则时发生错误: " & Err.Description, vbCritical
End Sub

' 加载同标签合成规则
Private Sub LoadSameTagRules()
    ' 方案A: 同标签升级合成
    Call AddSameTagUpgradeRules
    
    ' 方案B: 同标签转换合成
    Call AddSameTagConversionRules
End Sub

' 添加同标签升级规则
Private Sub AddSameTagUpgradeRules()
    ' T1同标签升级
    Dim t1Tags As Variant
    t1Tags = Array("火焰", "寒冰", "风暴", "大地", "生命", "死亡", "心灵", "灵魂")
    
    Dim i As Integer
    For i = LBound(t1Tags) To UBound(t1Tags)
        Dim tag As String
        tag = t1Tags(i)
        
        ' 同标签升级规则
        Dim upgradeKey As String
        upgradeKey = tag & " + " & tag
        
        ' 升级到强化版本（假设升级到对应的T2魔法）
        Dim upgradeResult As String
        upgradeResult = GetUpgradeResult(tag)
        
        If upgradeResult <> "" Then
            g_SameTagRules.Add upgradeKey, Array(upgradeResult, "Tier 1 -> Tier 2 (Upgrade)")
        End If
    Next i
    
    ' T2同标签升级
    Dim t2Tags As Variant
    t2Tags = Array("创造", "毁灭", "守护", "侵蚀", "扭曲", "净化")
    
    For i = LBound(t2Tags) To UBound(t2Tags)
        tag = t2Tags(i)
        upgradeKey = tag & " + " & tag
        upgradeResult = GetT2UpgradeResult(tag)
        
        If upgradeResult <> "" Then
            g_SameTagRules.Add upgradeKey, Array(upgradeResult, "Tier 2 -> Tier 3 (Upgrade)")
        End If
    Next i
    
    ' T3同标签升级
    Dim t3Tags As Variant
    t3Tags = Array("物质", "能量", "精神", "虚空")
    
    For i = LBound(t3Tags) To UBound(t3Tags)
        tag = t3Tags(i)
        upgradeKey = tag & " + " & tag
        upgradeResult = GetT3UpgradeResult(tag)
        
        If upgradeResult <> "" Then
            g_SameTagRules.Add upgradeKey, Array(upgradeResult, "Tier 3 -> Tier 4 (Upgrade)")
        End If
    Next i
End Sub

' 添加同标签转换规则
Private Sub AddSameTagConversionRules()
    ' T3同标签循环转换
    Dim t3Conversions As Variant
    t3Conversions = Array( _
        Array("物质", "能量"), _
        Array("能量", "精神"), _
        Array("精神", "虚空"), _
        Array("虚空", "物质") _
    )
    
    Dim i As Integer
    For i = LBound(t3Conversions) To UBound(t3Conversions)
        Dim fromTag As String, toTag As String
        fromTag = t3Conversions(i)(0)
        toTag = t3Conversions(i)(1)
        
        Dim conversionKey As String
        conversionKey = fromTag & " + " & fromTag & " (Convert)"
        
        g_SameTagRules.Add conversionKey, Array(toTag & "1", "Tier 3 -> Tier 3 (Convert)")
    Next i
End Sub

' 获取T1升级结果
Private Function GetUpgradeResult(t1Tag As String) As String
    ' 简单映射：T1标签升级到对应的T2魔法
    Select Case t1Tag
        Case "火焰", "风暴"
            GetUpgradeResult = "创造1"
        Case "寒冰", "大地"
            GetUpgradeResult = "守护1"
        Case "生命", "心灵"
            GetUpgradeResult = "净化1"
        Case "死亡", "灵魂"
            GetUpgradeResult = "侵蚀1"
        Case Else
            GetUpgradeResult = ""
    End Select
End Function

' 获取T2升级结果
Private Function GetT2UpgradeResult(t2Tag As String) As String
    Select Case t2Tag
        Case "创造", "守护"
            GetT2UpgradeResult = "物质1"
        Case "毁灭", "侵蚀"
            GetT2UpgradeResult = "能量1"
        Case "扭曲"
            GetT2UpgradeResult = "精神1"
        Case "净化"
            GetT2UpgradeResult = "虚空1"
        Case Else
            GetT2UpgradeResult = ""
    End Select
End Function

' 获取T3升级结果
Private Function GetT3UpgradeResult(t3Tag As String) As String
    Select Case t3Tag
        Case "物质", "精神"
            GetT3UpgradeResult = "因果1"
        Case "能量", "虚空"
            GetT3UpgradeResult = "循环1"
        Case Else
            GetT3UpgradeResult = ""
    End Select
End Function

' 获取反向组合键
Private Function GetReversedComboKey(comboKey As String) As String
    Dim parts As Variant
    parts = Split(comboKey, " + ")
    If UBound(parts) = 1 Then
        GetReversedComboKey = parts(1) & " + " & parts(0)
    Else
        GetReversedComboKey = comboKey
    End If
End Function

' 初始化魔法等级映射
Private Sub InitializeSameTagTierMap()
    ' T1魔法标签
    Dim t1Tags As Variant
    t1Tags = Array("火焰", "寒冰", "风暴", "大地", "生命", "死亡", "心灵", "灵魂")
    
    ' T2魔法标签
    Dim t2Tags As Variant
    t2Tags = Array("创造", "毁灭", "守护", "侵蚀", "扭曲", "净化")
    
    ' T3魔法标签
    Dim t3Tags As Variant
    t3Tags = Array("物质", "能量", "精神", "虚空")
    
    ' T4魔法标签
    Dim t4Tags As Variant
    t4Tags = Array("因果", "循环", "平衡")
    
    ' T5魔法标签
    Dim t5Tags As Variant
    t5Tags = Array("存在", "非在")
    
    ' T6魔法标签
    Dim t6Tags As Variant
    t6Tags = Array("奇点")
    
    ' 建立映射
    Call AddSameTagTagsToTierMap(t1Tags, 1)
    Call AddSameTagTagsToTierMap(t2Tags, 2)
    Call AddSameTagTagsToTierMap(t3Tags, 3)
    Call AddSameTagTagsToTierMap(t4Tags, 4)
    Call AddSameTagTagsToTierMap(t5Tags, 5)
    Call AddSameTagTagsToTierMap(t6Tags, 6)
End Sub

' 添加标签到等级映射
Private Sub AddSameTagTagsToTierMap(tags As Variant, tier As Integer)
    Dim i As Integer
    For i = LBound(tags) To UBound(tags)
        g_SameTagTierMap.Add tags(i), tier
    Next i
End Sub

' 寻找最佳合成选项（支持同标签）
Public Function FindSameTagBestSynthesis(playerPool As Object) As Variant
    ' 定义合成优先级
    Dim tierPriority As Object
    Set tierPriority = CreateObject("Scripting.Dictionary")
    tierPriority.Add "Tier 5 -> Tier 6", 10
    tierPriority.Add "Tier 4 -> Tier 5", 9
    tierPriority.Add "Tier 3 -> Tier 4", 8
    tierPriority.Add "Tier 3 -> Tier 4 (Upgrade)", 7
    tierPriority.Add "Tier 2 -> Tier 3", 6
    tierPriority.Add "Tier 2 -> Tier 3 (Upgrade)", 5
    tierPriority.Add "Tier 1 -> Tier 2", 4
    tierPriority.Add "Tier 1 -> Tier 2 (Upgrade)", 3
    tierPriority.Add "Tier 3 -> Tier 3 (Convert)", 2
    
    Dim bestSynthesis As Variant
    Dim maxPriority As Integer
    maxPriority = -1
    
    ' 遍历所有可能的魔法组合
    Dim keys As Variant
    keys = playerPool.Keys
    
    Dim i As Integer, j As Integer
    For i = 0 To UBound(keys)
        For j = i To UBound(keys)
            Dim magic1 As String, magic2 As String
            magic1 = CStr(keys(i))
            magic2 = CStr(keys(j))
            
            ' 检查是否有足够的魔法进行合成
            Dim count1 As Long, count2 As Long
            count1 = playerPool(magic1)
            count2 = playerPool(magic2)
            
            Dim canSynthesize As Boolean
            If magic1 = magic2 Then
                canSynthesize = count1 >= 2
            Else
                canSynthesize = count1 >= 1 And count2 >= 1
            End If
            
            If canSynthesize Then
                ' 检查合成规则
                Dim tag1 As String, tag2 As String
                tag1 = GetSameTagMagicTag(magic1)
                tag2 = GetSameTagMagicTag(magic2)
                
                Dim comboKey As String
                comboKey = tag1 & " + " & tag2
                
                ' 检查是否存在合成规则（现在允许同标签）
                If g_SameTagRules.Exists(comboKey) Then
                    Dim ruleInfo As Variant
                    ruleInfo = g_SameTagRules(comboKey)
                    
                    Dim resultMagic As String
                    Dim tierTransition As String
                    resultMagic = ruleInfo(0)
                    tierTransition = ruleInfo(1)
                    
                    ' 计算优先级
                    Dim priority As Integer
                    If tierPriority.Exists(tierTransition) Then
                        priority = tierPriority(tierTransition)
                    Else
                        priority = 0
                    End If
                    
                    ' 选择优先级最高的合成
                    If priority > maxPriority Then
                        maxPriority = priority
                        bestSynthesis = Array(magic1, magic2, resultMagic, tierTransition, priority)
                    End If
                End If
            End If
        Next j
    Next i
    
    If maxPriority = -1 Then
        FindSameTagBestSynthesis = Empty
    Else
        FindSameTagBestSynthesis = bestSynthesis
    End If
End Function

' 从魔法名称提取标签
Public Function GetSameTagMagicTag(magicName As String) As String
    Dim i As Integer
    For i = Len(magicName) To 1 Step -1
        If Not IsNumeric(Mid(magicName, i, 1)) Then
            GetSameTagMagicTag = Left(magicName, i)
            Exit Function
        End If
    Next i
    GetSameTagMagicTag = magicName
End Function

' 获取魔法的等级
Public Function GetSameTagMagicTier(magicName As String) As Integer
    Dim tag As String
    tag = GetSameTagMagicTag(magicName)
    
    If g_SameTagTierMap.Exists(tag) Then
        GetSameTagMagicTier = g_SameTagTierMap(tag)
    Else
        GetSameTagMagicTier = 0
    End If
End Function

' 测试同标签合成系统
Public Sub TestSameTagSystem()
    On Error GoTo ErrorHandler
    
    ' 初始化系统
    Call InitializeSameTagSystem
    
    Dim testResult As String
    testResult = "=== 同标签合成系统测试 ===" & vbCrLf & vbCrLf
    testResult = testResult & "总规则数量: " & g_SameTagRules.Count & vbCrLf & vbCrLf
    
    ' 测试同标签合成规则
    Dim sameTagTests As Variant
    sameTagTests = Array("火焰 + 火焰", "物质 + 物质", "因果 + 因果")
    
    Dim i As Integer
    For i = LBound(sameTagTests) To UBound(sameTagTests)
        Dim testKey As String
        testKey = sameTagTests(i)
        
        If g_SameTagRules.Exists(testKey) Then
            Dim ruleInfo As Variant
            ruleInfo = g_SameTagRules(testKey)
            testResult = testResult & "✅ " & testKey & " -> " & ruleInfo(0) & " (" & ruleInfo(1) & ")" & vbCrLf
        Else
            testResult = testResult & "❌ " & testKey & " - 规则不存在" & vbCrLf
        End If
    Next i
    
    ' 测试死路场景解决
    testResult = testResult & vbCrLf & "死路场景测试:" & vbCrLf
    
    Dim deadlockPool As Object
    Set deadlockPool = CreateObject("Scripting.Dictionary")
    deadlockPool.Add "物质1", 4
    
    Dim bestSynthesis As Variant
    bestSynthesis = FindSameTagBestSynthesis(deadlockPool)
    
    If IsEmpty(bestSynthesis) Then
        testResult = testResult & "❌ 仍然存在死路" & vbCrLf
    Else
        testResult = testResult & "✅ 死路已解决: " & bestSynthesis(0) & " + " & bestSynthesis(1) & " -> " & bestSynthesis(2) & vbCrLf
    End If
    
    MsgBox testResult, vbInformation, "同标签系统测试"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "测试同标签系统时发生错误: " & Err.Description, vbCritical
End Sub
