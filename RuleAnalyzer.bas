Option Explicit

'==============================================================================
' 合成规则分析器
' 分析当前规则的完整性和可能的死路问题
'==============================================================================

' 分析规则完整性
Public Sub AnalyzeRuleCompleteness()
    On Error GoTo ErrorHandler
    
    ' 初始化系统
    Call InitializeOptimizedSystem
    
    Dim analysis As String
    analysis = "=== 合成规则完整性分析 ===" & vbCrLf & vbCrLf
    
    ' 1. 分析各等级的合成路径
    analysis = analysis & "【各等级合成路径分析】" & vbCrLf
    
    ' T1->T2分析
    analysis = analysis & "T1->T2: " & CountRulesByTier("Tier 1 -> Tier 2") & " 条规则" & vbCrLf
    
    ' T2->T3分析
    analysis = analysis & "T2->T3: " & CountRulesByTier("Tier 2 -> Tier 3") & " 条规则" & vbCrLf
    
    ' T3->T4分析
    analysis = analysis & "T3->T4: " & CountRulesByTier("Tier 3 -> Tier 4") & " 条规则" & vbCrLf
    
    ' T4->T5分析
    analysis = analysis & "T4->T5: " & CountRulesByTier("Tier 4 -> Tier 5") & " 条规则" & vbCrLf
    
    ' T5->T6分析
    analysis = analysis & "T5->T6: " & CountRulesByTier("Tier 5 -> Tier 6") & " 条规则" & vbCrLf & vbCrLf
    
    ' 2. 分析可能的死路
    analysis = analysis & "【潜在死路分析】" & vbCrLf
    analysis = analysis & AnalyzeDeadlocks()
    
    ' 3. 建议的解决方案
    analysis = analysis & vbCrLf & "【建议的解决方案】" & vbCrLf
    analysis = analysis & GetSolutionSuggestions()
    
    MsgBox analysis, vbInformation, "规则完整性分析"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "分析规则时发生错误: " & Err.Description, vbCritical
End Sub

' 统计指定等级的规则数量
Private Function CountRulesByTier(tierTransition As String) As Integer
    Dim count As Integer
    count = 0
    
    Dim key As Variant
    For Each key In g_OptimizedRules.Keys
        Dim ruleInfo As Variant
        ruleInfo = g_OptimizedRules(key)
        
        If ruleInfo(1) = tierTransition Then
            count = count + 1
        End If
    Next key
    
    CountRulesByTier = count
End Function

' 分析死路情况
Private Function AnalyzeDeadlocks() As String
    Dim deadlockAnalysis As String
    deadlockAnalysis = ""
    
    ' 分析T3阶段的死路风险
    deadlockAnalysis = deadlockAnalysis & "T3阶段死路风险:" & vbCrLf
    
    ' 检查T3魔法的配对情况
    Dim t3Tags As Variant
    t3Tags = Array("物质", "能量", "精神", "虚空")
    
    Dim t3Pairs As Variant
    t3Pairs = Array( _
        Array("物质", "能量"), _
        Array("精神", "虚空"), _
        Array("物质", "精神"), _
        Array("精神", "能量"), _
        Array("虚空", "物质"), _
        Array("能量", "虚空") _
    )
    
    deadlockAnalysis = deadlockAnalysis & "  必需的T3配对组合:" & vbCrLf
    
    Dim i As Integer
    For i = LBound(t3Pairs) To UBound(t3Pairs)
        Dim tag1 As String, tag2 As String
        tag1 = t3Pairs(i)(0)
        tag2 = t3Pairs(i)(1)
        
        Dim comboKey As String
        comboKey = tag1 & " + " & tag2
        
        If g_OptimizedRules.Exists(comboKey) Then
            Dim ruleInfo As Variant
            ruleInfo = g_OptimizedRules(comboKey)
            deadlockAnalysis = deadlockAnalysis & "    ✅ " & comboKey & " -> " & ruleInfo(0) & vbCrLf
        Else
            deadlockAnalysis = deadlockAnalysis & "    ❌ " & comboKey & " - 缺失" & vbCrLf
        End If
    Next i
    
    deadlockAnalysis = deadlockAnalysis & vbCrLf
    deadlockAnalysis = deadlockAnalysis & "  风险评估: 如果玩家过度合成单一T3魔法，可能无法配对" & vbCrLf
    
    AnalyzeDeadlocks = deadlockAnalysis
End Function

' 获取解决方案建议
Private Function GetSolutionSuggestions() As String
    Dim suggestions As String
    suggestions = ""
    
    suggestions = suggestions & "方案1: 添加同标签强化合成" & vbCrLf
    suggestions = suggestions & "  物质1 + 物质1 = 物质2" & vbCrLf
    suggestions = suggestions & "  物质2 + 能量1 = 因果1 (降低要求)" & vbCrLf & vbCrLf
    
    suggestions = suggestions & "方案2: 添加魔法转换循环" & vbCrLf
    suggestions = suggestions & "  物质1 x2 = 能量1" & vbCrLf
    suggestions = suggestions & "  能量1 x2 = 精神1" & vbCrLf
    suggestions = suggestions & "  精神1 x2 = 虚空1" & vbCrLf
    suggestions = suggestions & "  虚空1 x2 = 物质1" & vbCrLf & vbCrLf
    
    suggestions = suggestions & "方案3: 添加应急合成" & vbCrLf
    suggestions = suggestions & "  任意T3魔法 x3 = 随机T4魔法" & vbCrLf
    
    GetSolutionSuggestions = suggestions
End Function

' 模拟死路场景
Public Sub SimulateDeadlockScenario()
    On Error GoTo ErrorHandler
    
    MsgBox "模拟死路场景..." & vbCrLf & vbCrLf & _
           "假设玩家过度合成了物质类魔法", vbInformation
    
    ' 创建一个死路场景的魔法池
    Dim deadlockPool As Object
    Set deadlockPool = CreateObject("Scripting.Dictionary")
    
    ' 模拟玩家过度合成物质魔法的情况
    deadlockPool.Add "物质1", 4  ' 大量物质魔法
    deadlockPool.Add "精神1", 1  ' 少量其他魔法
    
    Dim scenario As String
    scenario = "死路场景模拟:" & vbCrLf & vbCrLf
    scenario = scenario & "当前魔法池: 物质1 x4, 精神1 x1" & vbCrLf & vbCrLf
    
    ' 初始化系统
    Call InitializeOptimizedSystem
    
    ' 尝试寻找合成选项
    Dim bestSynthesis As Variant
    bestSynthesis = FindOptimizedBestSynthesis(deadlockPool)
    
    If IsEmpty(bestSynthesis) Then
        scenario = scenario & "结果: ❌ 无法找到任何合成选项！" & vbCrLf
        scenario = scenario & "原因: 缺少配对的T3魔法进行T3->T4合成" & vbCrLf & vbCrLf
        scenario = scenario & "这就是典型的死路场景！"
    Else
        scenario = scenario & "结果: ✅ 找到合成选项: " & bestSynthesis(2) & vbCrLf
        scenario = scenario & "说明: 当前规则下仍可继续合成"
    End If
    
    MsgBox scenario, vbInformation, "死路场景结果"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "模拟死路场景时发生错误: " & Err.Description, vbCritical
End Sub

' 生成补充规则建议
Public Sub GenerateSupplementaryRules()
    On Error GoTo ErrorHandler
    
    Dim rules As String
    rules = "=== 建议添加的补充规则 ===" & vbCrLf & vbCrLf
    
    ' 1. 同标签强化规则
    rules = rules & "【同标签强化规则】" & vbCrLf
    rules = rules & "物质1 + 物质1 = 物质2" & vbCrLf
    rules = rules & "能量1 + 能量1 = 能量2" & vbCrLf
    rules = rules & "精神1 + 精神1 = 精神2" & vbCrLf
    rules = rules & "虚空1 + 虚空1 = 虚空2" & vbCrLf & vbCrLf
    
    ' 2. 降级合成规则
    rules = rules & "【降级合成规则】" & vbCrLf
    rules = rules & "物质2 + 能量1 = 因果1" & vbCrLf
    rules = rules & "能量2 + 精神1 = 因果1" & vbCrLf
    rules = rules & "精神2 + 虚空1 = 循环1" & vbCrLf
    rules = rules & "虚空2 + 物质1 = 循环1" & vbCrLf & vbCrLf
    
    ' 3. 转换规则
    rules = rules & "【魔法转换规则】" & vbCrLf
    rules = rules & "物质1 + 物质1 = 能量1 (转换)" & vbCrLf
    rules = rules & "能量1 + 能量1 = 精神1 (转换)" & vbCrLf
    rules = rules & "精神1 + 精神1 = 虚空1 (转换)" & vbCrLf
    rules = rules & "虚空1 + 虚空1 = 物质1 (转换)" & vbCrLf & vbCrLf
    
    ' 4. 应急规则
    rules = rules & "【应急合成规则】" & vbCrLf
    rules = rules & "任意T3魔法 x3 = 随机T4魔法" & vbCrLf
    rules = rules & "任意T4魔法 x2 = 随机T5魔法" & vbCrLf & vbCrLf
    
    rules = rules & "【实施建议】" & vbCrLf
    rules = rules & "1. 优先实施同标签强化规则（最简单）" & vbCrLf
    rules = rules & "2. 考虑添加转换规则（最优雅）" & vbCrLf
    rules = rules & "3. 应急规则作为最后保障" & vbCrLf
    
    MsgBox rules, vbInformation, "补充规则建议"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "生成规则建议时发生错误: " & Err.Description, vbCritical
End Sub

' 测试补充规则的效果
Public Sub TestSupplementaryRules()
    On Error GoTo ErrorHandler
    
    MsgBox "测试补充规则的效果..." & vbCrLf & vbCrLf & _
           "将模拟添加同标签强化规则后的效果", vbInformation
    
    ' 创建死路场景
    Dim testPool As Object
    Set testPool = CreateObject("Scripting.Dictionary")
    testPool.Add "物质1", 4
    testPool.Add "精神1", 1
    
    Dim testResult As String
    testResult = "补充规则测试:" & vbCrLf & vbCrLf
    testResult = testResult & "原始魔法池: 物质1 x4, 精神1 x1" & vbCrLf & vbCrLf
    
    ' 模拟应用补充规则
    testResult = testResult & "应用补充规则后的可能合成:" & vbCrLf
    testResult = testResult & "1. 物质1 + 物质1 = 物质2" & vbCrLf
    testResult = testResult & "2. 物质2 + 精神1 = 因果1 (如果有此规则)" & vbCrLf
    testResult = testResult & "或者:" & vbCrLf
    testResult = testResult & "1. 物质1 + 物质1 = 能量1 (转换规则)" & vbCrLf
    testResult = testResult & "2. 能量1 + 精神1 = 因果1" & vbCrLf & vbCrLf
    
    testResult = testResult & "结论: 补充规则可以有效解决死路问题！"
    
    MsgBox testResult, vbInformation, "补充规则测试结果"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "测试补充规则时发生错误: " & Err.Description, vbCritical
End Sub

' 显示分析菜单
Public Sub ShowAnalysisMenu()
    Dim choice As String
    choice = InputBox( _
        "=== 规则分析工具菜单 ===" & vbCrLf & vbCrLf & _
        "1 - 分析规则完整性" & vbCrLf & _
        "2 - 模拟死路场景" & vbCrLf & _
        "3 - 生成补充规则建议" & vbCrLf & _
        "4 - 测试补充规则效果" & vbCrLf & vbCrLf & _
        "请输入选项编号:", _
        "规则分析", "1")
    
    Select Case choice
        Case "1"
            Call AnalyzeRuleCompleteness
        Case "2"
            Call SimulateDeadlockScenario
        Case "3"
            Call GenerateSupplementaryRules
        Case "4"
            Call TestSupplementaryRules
        Case Else
            If choice <> "" Then
                MsgBox "无效的选项！", vbExclamation
            End If
    End Select
End Sub
