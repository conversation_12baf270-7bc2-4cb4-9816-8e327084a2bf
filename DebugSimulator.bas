Option Explicit

'==============================================================================
' 调试模拟器
' 用于诊断模拟器无法进行高等级合成的问题
'==============================================================================

' 调试单次完整模拟过程
Public Sub DebugFullSimulation()
    On Error GoTo ErrorHandler
    
    ' 初始化系统
    Call InitializeOptimizedSystem
    
    ' 读取魔法池
    Dim playerPool As Object
    Set playerPool = ReadMagicPoolFromExcel()
    
    If playerPool.Count = 0 Then
        MsgBox "魔法池为空！", vbExclamation
        Exit Sub
    End If
    
    MsgBox "开始调试模拟..." & vbCrLf & vbCrLf & _
           "初始魔法池: " & GetDebugPoolSummary(playerPool), vbInformation
    
    ' 执行详细的单次模拟
    Call RunDetailedDebugSimulation(playerPool)
    
    Exit Sub
    
ErrorHandler:
    MsgBox "调试过程中发生错误: " & Err.Description, vbCritical
End Sub

' 运行详细的调试模拟
Private Sub RunDetailedDebugSimulation(initialPool As Object)
    ' 复制魔法池
    Dim playerPool As Object
    Set playerPool = CloneDictionary(initialPool)
    
    Dim step As Integer
    step = 0
    Dim maxSteps As Integer
    maxSteps = 50
    
    Dim log As String
    log = "=== 详细模拟日志 ===" & vbCrLf & vbCrLf
    
    Do While step < maxSteps
        step = step + 1
        
        ' 检查是否已有T6魔法
        If HasOptimizedMagicOfTier(playerPool, 6) Then
            log = log & "第" & step & "步: ✅ 已获得T6魔法！模拟成功！" & vbCrLf
            Exit Do
        End If
        
        ' 显示当前魔法池状态
        log = log & "第" & step & "步开始:" & vbCrLf
        log = log & "  当前魔法池: " & GetDebugPoolSummary(playerPool) & vbCrLf
        
        ' 寻找最佳合成选项
        Dim bestSynthesis As Variant
        bestSynthesis = FindOptimizedBestSynthesis(playerPool)
        
        If IsEmpty(bestSynthesis) Then
            log = log & "  ❌ 无法找到合成选项，模拟结束" & vbCrLf
            log = log & "  最终魔法池: " & GetDebugPoolSummary(playerPool) & vbCrLf
            Exit Do
        End If
        
        ' 记录合成信息
        log = log & "  选择合成: " & bestSynthesis(0) & " + " & bestSynthesis(1) & " -> " & bestSynthesis(2) & vbCrLf
        log = log & "  合成类型: " & bestSynthesis(3) & vbCrLf
        
        ' 执行合成
        Dim dummyStats As Object
        Set dummyStats = CreateObject("Scripting.Dictionary")
        Call ExecuteOptimizedSynthesis(playerPool, bestSynthesis, dummyStats)
        
        log = log & "  合成后: " & GetDebugPoolSummary(playerPool) & vbCrLf & vbCrLf
        
        ' 每10步显示一次日志
        If step Mod 10 = 0 Then
            If MsgBox(log & "继续模拟？", vbYesNo + vbQuestion, "调试日志 - 第" & step & "步") = vbNo Then
                Exit Do
            End If
            log = "=== 继续模拟日志 ===" & vbCrLf & vbCrLf
        End If
    Loop
    
    ' 显示最终日志
    MsgBox log, vbInformation, "最终调试日志"
End Sub

' 获取调试用的魔法池摘要
Private Function GetDebugPoolSummary(playerPool As Object) As String
    Dim summary As String
    summary = ""
    
    ' 按等级分组
    Dim tierGroups(1 To 6) As String
    Dim i As Integer
    For i = 1 To 6
        tierGroups(i) = ""
    Next i
    
    Dim key As Variant
    For Each key In playerPool.Keys
        Dim tier As Integer
        tier = GetOptimizedMagicTier(CStr(key))
        
        If tier >= 1 And tier <= 6 Then
            If tierGroups(tier) <> "" Then tierGroups(tier) = tierGroups(tier) & ", "
            tierGroups(tier) = tierGroups(tier) & key & "x" & playerPool(key)
        End If
    Next key
    
    For i = 1 To 6
        If tierGroups(i) <> "" Then
            If summary <> "" Then summary = summary & " | "
            summary = summary & "T" & i & ":[" & tierGroups(i) & "]"
        End If
    Next i
    
    If summary = "" Then summary = "空"
    GetDebugPoolSummary = summary
End Function

' 复制Dictionary对象
Private Function CloneDictionary(sourceDict As Object) As Object
    Dim newDict As Object
    Set newDict = CreateObject("Scripting.Dictionary")
    
    Dim key As Variant
    For Each key In sourceDict.Keys
        newDict.Add key, sourceDict(key)
    Next key
    
    Set CloneDictionary = newDict
End Function

' 测试特定的合成规则
Public Sub TestSpecificRule()
    On Error GoTo ErrorHandler
    
    ' 初始化系统
    Call InitializeOptimizedSystem
    
    ' 创建测试魔法池 - 包含T3魔法
    Dim testPool As Object
    Set testPool = CreateObject("Scripting.Dictionary")
    testPool.Add "物质1", 1
    testPool.Add "能量1", 1
    
    MsgBox "测试特定规则:" & vbCrLf & vbCrLf & _
           "测试魔法池: 物质1 x1, 能量1 x1" & vbCrLf & _
           "期望合成: 物质1 + 能量1 -> 因果1", vbInformation
    
    ' 检查规则是否存在
    Dim ruleExists As Boolean
    ruleExists = g_OptimizedRules.Exists("物质 + 能量")
    
    Dim testResult As String
    testResult = "规则测试结果:" & vbCrLf & vbCrLf
    
    If ruleExists Then
        Dim ruleInfo As Variant
        ruleInfo = g_OptimizedRules("物质 + 能量")
        testResult = testResult & "✅ 规则存在: 物质 + 能量 -> " & ruleInfo(0) & " (" & ruleInfo(1) & ")" & vbCrLf & vbCrLf
    Else
        testResult = testResult & "❌ 规则不存在: 物质 + 能量" & vbCrLf & vbCrLf
    End If
    
    ' 测试合成查找
    Dim bestSynthesis As Variant
    bestSynthesis = FindOptimizedBestSynthesis(testPool)
    
    If IsEmpty(bestSynthesis) Then
        testResult = testResult & "❌ 无法找到合成选项" & vbCrLf
        testResult = testResult & "可能原因:" & vbCrLf
        testResult = testResult & "1. 魔法标签提取错误" & vbCrLf
        testResult = testResult & "2. 合成规则匹配失败" & vbCrLf
        testResult = testResult & "3. 优先级计算问题" & vbCrLf
    Else
        testResult = testResult & "✅ 找到合成选项!" & vbCrLf
        testResult = testResult & "材料: " & bestSynthesis(0) & " + " & bestSynthesis(1) & vbCrLf
        testResult = testResult & "结果: " & bestSynthesis(2) & vbCrLf
        testResult = testResult & "类型: " & bestSynthesis(3) & vbCrLf
        testResult = testResult & "优先级: " & bestSynthesis(4) & vbCrLf
    End If
    
    MsgBox testResult, vbInformation, "规则测试"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "测试规则时发生错误: " & Err.Description, vbCritical
End Sub

' 测试魔法标签提取
Public Sub TestMagicTagExtraction()
    On Error GoTo ErrorHandler
    
    ' 初始化系统
    Call InitializeOptimizedSystem
    
    Dim testMagics As Variant
    testMagics = Array("火焰1", "物质1", "因果1", "存在1", "奇点1")
    
    Dim testResult As String
    testResult = "魔法标签提取测试:" & vbCrLf & vbCrLf
    
    Dim i As Integer
    For i = LBound(testMagics) To UBound(testMagics)
        Dim magicName As String
        magicName = testMagics(i)
        
        Dim tag As String
        tag = GetOptimizedMagicTag(magicName)
        
        Dim tier As Integer
        tier = GetOptimizedMagicTier(magicName)
        
        testResult = testResult & magicName & " -> 标签: " & tag & ", 等级: T" & tier & vbCrLf
    Next i
    
    MsgBox testResult, vbInformation, "标签提取测试"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "测试标签提取时发生错误: " & Err.Description, vbCritical
End Sub

' 分析为什么无法进行T3->T4合成
Public Sub AnalyzeT3T4Problem()
    On Error GoTo ErrorHandler
    
    ' 初始化系统
    Call InitializeOptimizedSystem
    
    ' 读取实际魔法池
    Dim playerPool As Object
    Set playerPool = ReadMagicPoolFromExcel()
    
    ' 模拟到T3阶段
    Dim simulatedPool As Object
    Set simulatedPool = CreateObject("Scripting.Dictionary")
    
    ' 添加一些T3魔法进行测试
    simulatedPool.Add "物质1", 2
    simulatedPool.Add "能量1", 2
    simulatedPool.Add "精神1", 1
    simulatedPool.Add "虚空1", 1
    
    Dim analysis As String
    analysis = "=== T3->T4合成问题分析 ===" & vbCrLf & vbCrLf
    analysis = analysis & "模拟T3魔法池: " & GetDebugPoolSummary(simulatedPool) & vbCrLf & vbCrLf
    
    ' 检查所有可能的T3->T4合成
    analysis = analysis & "检查T3->T4合成规则:" & vbCrLf
    
    Dim t3Rules As Variant
    t3Rules = Array( _
        Array("物质", "能量", "因果1"), _
        Array("精神", "虚空", "循环1"), _
        Array("物质", "精神", "平衡1"), _
        Array("精神", "能量", "因果1"), _
        Array("虚空", "物质", "循环1"), _
        Array("能量", "虚空", "平衡1") _
    )
    
    Dim i As Integer
    For i = LBound(t3Rules) To UBound(t3Rules)
        Dim tag1 As String, tag2 As String, expectedResult As String
        tag1 = t3Rules(i)(0)
        tag2 = t3Rules(i)(1)
        expectedResult = t3Rules(i)(2)
        
        Dim comboKey As String
        comboKey = tag1 & " + " & tag2
        
        If g_OptimizedRules.Exists(comboKey) Then
            Dim ruleInfo As Variant
            ruleInfo = g_OptimizedRules(comboKey)
            analysis = analysis & "  ✅ " & comboKey & " -> " & ruleInfo(0) & vbCrLf
        Else
            analysis = analysis & "  ❌ " & comboKey & " - 规则缺失" & vbCrLf
        End If
    Next i
    
    analysis = analysis & vbCrLf & "尝试寻找合成选项:" & vbCrLf
    
    Dim bestSynthesis As Variant
    bestSynthesis = FindOptimizedBestSynthesis(simulatedPool)
    
    If IsEmpty(bestSynthesis) Then
        analysis = analysis & "❌ 无法找到任何合成选项！" & vbCrLf
        analysis = analysis & vbCrLf & "可能的问题:" & vbCrLf
        analysis = analysis & "1. 合成规则匹配逻辑有误" & vbCrLf
        analysis = analysis & "2. 优先级计算有问题" & vbCrLf
        analysis = analysis & "3. 魔法池状态检查有误" & vbCrLf
    Else
        analysis = analysis & "✅ 找到合成选项: " & bestSynthesis(0) & " + " & bestSynthesis(1) & " -> " & bestSynthesis(2) & vbCrLf
    End If
    
    MsgBox analysis, vbInformation, "T3->T4问题分析"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "分析T3->T4问题时发生错误: " & Err.Description, vbCritical
End Sub

' 显示调试工具菜单
Public Sub ShowDebugMenu()
    Dim choice As String
    choice = InputBox( _
        "=== 调试工具菜单 ===" & vbCrLf & vbCrLf & _
        "1 - 调试完整模拟过程" & vbCrLf & _
        "2 - 测试特定合成规则" & vbCrLf & _
        "3 - 测试魔法标签提取" & vbCrLf & _
        "4 - 分析T3->T4合成问题" & vbCrLf & vbCrLf & _
        "请输入选项编号:", _
        "调试工具", "1")
    
    Select Case choice
        Case "1"
            Call DebugFullSimulation
        Case "2"
            Call TestSpecificRule
        Case "3"
            Call TestMagicTagExtraction
        Case "4"
            Call AnalyzeT3T4Problem
        Case Else
            If choice <> "" Then
                MsgBox "无效的选项！", vbExclamation
            End If
    End Select
End Sub
