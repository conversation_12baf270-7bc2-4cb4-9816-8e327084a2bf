Option Explicit

'==============================================================================
' 魔法模拟器演示脚本
' 展示高级魔法合成模拟器的各种使用场景
'==============================================================================

' 演示1: 基础模拟演示
Public Sub Demo1_BasicSimulation()
    MsgBox "演示1: 基础模拟" & vbCrLf & vbCrLf & _
           "这个演示将运行100次模拟，每个玩家初始拥有20个T1魔法。" & vbCrLf & _
           "点击确定开始演示...", vbInformation
    
    On Error GoTo ErrorHandler
    
    Application.ScreenUpdating = False
    
    ' 初始化系统
    Call InitializeSimulationSystem
    
    ' 运行模拟
    Dim aggregatedResults As Object
    Set aggregatedResults = RunBatchSimulation(100, 20)
    
    ' 输出结果
    Call OutputSimulationResults(aggregatedResults, 100)
    
    Application.ScreenUpdating = True
    
    MsgBox "演示1完成！请查看统计数据工作表中的结果。", vbInformation
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    MsgBox "演示1失败: " & Err.Description, vbCritical
End Sub

' 演示2: 不同初始条件对比
Public Sub Demo2_CompareInitialConditions()
    MsgBox "演示2: 不同初始条件对比" & vbCrLf & vbCrLf & _
           "这个演示将对比不同初始魔法数量对成功率的影响。" & vbCrLf & _
           "将分别测试10、20、30个初始魔法的情况。" & vbCrLf & _
           "点击确定开始演示...", vbInformation
    
    On Error GoTo ErrorHandler
    
    Application.ScreenUpdating = False
    
    ' 初始化系统
    Call InitializeSimulationSystem
    
    Dim wsStats As Worksheet
    Set wsStats = ThisWorkbook.Sheets("统计数据")
    wsStats.Range("A1:J1000").ClearContents
    
    ' 输出标题
    wsStats.Range("A1").Value = "不同初始条件对比演示"
    wsStats.Range("A3").Value = "初始魔法数量"
    wsStats.Range("B3").Value = "成功次数"
    wsStats.Range("C3").Value = "成功率"
    wsStats.Range("D3").Value = "平均合成轮数"
    
    Dim initialCounts As Variant
    initialCounts = Array(10, 20, 30)
    
    Dim i As Integer
    For i = LBound(initialCounts) To UBound(initialCounts)
        Dim initialCount As Integer
        initialCount = initialCounts(i)
        
        Application.StatusBar = "测试初始魔法数量: " & initialCount
        
        ' 运行模拟
        Dim aggregatedResults As Object
        Set aggregatedResults = RunBatchSimulation(200, initialCount)
        
        ' 输出结果
        Dim row As Integer
        row = 4 + i
        wsStats.Cells(row, 1).Value = initialCount
        wsStats.Cells(row, 2).Value = aggregatedResults("SuccessCount")
        wsStats.Cells(row, 3).Value = Format(aggregatedResults("SuccessCount") / 200, "0.00%")
        wsStats.Cells(row, 4).Value = Format(aggregatedResults("TotalIterations") / 200, "0.0")
    Next i
    
    Application.StatusBar = False
    Application.ScreenUpdating = True
    
    MsgBox "演示2完成！请查看统计数据工作表中的对比结果。", vbInformation
    Exit Sub
    
ErrorHandler:
    Application.StatusBar = False
    Application.ScreenUpdating = True
    MsgBox "演示2失败: " & Err.Description, vbCritical
End Sub

' 演示3: 合成路径分析
Public Sub Demo3_SynthesisPathAnalysis()
    MsgBox "演示3: 合成路径分析" & vbCrLf & vbCrLf & _
           "这个演示将详细分析合成路径，展示各阶级转换的频率。" & vbCrLf & _
           "点击确定开始演示...", vbInformation
    
    On Error GoTo ErrorHandler
    
    Application.ScreenUpdating = False
    
    ' 初始化系统
    Call InitializeSimulationSystem
    
    ' 运行模拟
    Dim aggregatedResults As Object
    Set aggregatedResults = RunBatchSimulation(500, 25)
    
    ' 详细分析合成路径
    Call AnalyzeSynthesisPath(aggregatedResults, 500)
    
    Application.ScreenUpdating = True
    
    MsgBox "演示3完成！请查看统计数据工作表中的详细分析。", vbInformation
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    MsgBox "演示3失败: " & Err.Description, vbCritical
End Sub

' 演示4: 实时模拟观察
Public Sub Demo4_RealTimeSimulation()
    MsgBox "演示4: 实时模拟观察" & vbCrLf & vbCrLf & _
           "这个演示将运行一次详细的单人模拟，" & vbCrLf & _
           "展示每一步的合成过程。" & vbCrLf & _
           "点击确定开始演示...", vbInformation
    
    On Error GoTo ErrorHandler
    
    Application.ScreenUpdating = False
    
    ' 初始化系统
    Call InitializeSimulationSystem
    
    ' 运行详细的单次模拟
    Call RunDetailedSingleSimulation(30)
    
    Application.ScreenUpdating = True
    
    MsgBox "演示4完成！", vbInformation
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    MsgBox "演示4失败: " & Err.Description, vbCritical
End Sub

' 分析合成路径
Private Sub AnalyzeSynthesisPath(aggregatedResults As Object, simulationCount As Long)
    Dim wsStats As Worksheet
    Set wsStats = ThisWorkbook.Sheets("统计数据")
    wsStats.Range("A1:J1000").ClearContents
    
    ' 输出标题
    wsStats.Range("A1").Value = "合成路径详细分析"
    wsStats.Range("A2").Value = "模拟次数: " & simulationCount
    wsStats.Range("A3").Value = "成功率: " & Format(aggregatedResults("SuccessCount") / simulationCount, "0.00%")
    
    ' 合成统计
    wsStats.Range("A5").Value = "合成阶级详细统计:"
    wsStats.Range("A6").Value = "阶级转换"
    wsStats.Range("B6").Value = "总次数"
    wsStats.Range("C6").Value = "平均次数"
    wsStats.Range("D6").Value = "成功模拟中的平均次数"
    wsStats.Range("E6").Value = "占比"
    wsStats.Range("F6").Value = "效率评分"
    
    Dim row As Long
    row = 7
    
    If aggregatedResults.Exists("SynthesisStats") Then
        Dim synthesisStats As Object
        Set synthesisStats = aggregatedResults("SynthesisStats")
        
        Dim totalSyntheses As Long
        totalSyntheses = 0
        
        Dim key As Variant
        For Each key In synthesisStats.Keys
            totalSyntheses = totalSyntheses + synthesisStats(key)
        Next key
        
        ' 按优先级排序输出
        Dim tierOrder As Variant
        tierOrder = Array("T5->T6", "T4->T5", "T3->T4", "T2->T3", "T1->T2")
        
        Dim i As Integer
        For i = LBound(tierOrder) To UBound(tierOrder)
            Dim tierTransition As String
            tierTransition = tierOrder(i)
            
            If synthesisStats.Exists(tierTransition) Then
                Dim count As Long
                count = synthesisStats(tierTransition)
                
                wsStats.Cells(row, 1).Value = tierTransition
                wsStats.Cells(row, 2).Value = count
                wsStats.Cells(row, 3).Value = Format(count / simulationCount, "0.0")
                wsStats.Cells(row, 4).Value = Format(count / aggregatedResults("SuccessCount"), "0.0")
                wsStats.Cells(row, 5).Value = Format(count / totalSyntheses, "0.00%")
                
                ' 计算效率评分（高等级转换得分更高）
                Dim efficiency As Double
                Select Case tierTransition
                    Case "T5->T6": efficiency = count * 5
                    Case "T4->T5": efficiency = count * 4
                    Case "T3->T4": efficiency = count * 3
                    Case "T2->T3": efficiency = count * 2
                    Case "T1->T2": efficiency = count * 1
                End Select
                wsStats.Cells(row, 6).Value = Format(efficiency / simulationCount, "0.0")
                
                row = row + 1
            End If
        Next i
    End If
    
    ' 格式化表格
    With wsStats.Range("A6:F6")
        .Font.Bold = True
        .Interior.Color = RGB(200, 200, 200)
        .Borders.LineStyle = xlContinuous
    End With
    
    wsStats.Columns("A:F").AutoFit
End Sub

' 运行详细的单次模拟
Private Sub RunDetailedSingleSimulation(initialMagicCount As Long)
    Dim playerPool As Object
    Set playerPool = CreateObject("Scripting.Dictionary")
    
    Dim synthesisLog As String
    synthesisLog = "详细单次模拟日志:" & vbCrLf & vbCrLf
    
    ' 初始化玩家魔法池
    Call InitializePlayerPool(playerPool, initialMagicCount)
    
    synthesisLog = synthesisLog & "初始魔法池 (" & GetTotalMagicCount(playerPool) & " 个魔法):" & vbCrLf
    synthesisLog = synthesisLog & GetPoolSummary(playerPool) & vbCrLf & vbCrLf
    
    ' 执行合成
    Dim iteration As Integer
    iteration = 0
    Dim maxIterations As Integer
    maxIterations = 50
    
    Do While iteration < maxIterations
        iteration = iteration + 1
        
        ' 检查是否已有T6魔法
        If HasMagicOfTier(playerPool, 6) Then
            synthesisLog = synthesisLog & "第" & iteration & "轮: 已获得T6魔法！模拟成功！" & vbCrLf
            Exit Do
        End If
        
        ' 寻找最佳合成选项
        Dim bestSynthesis As Variant
        bestSynthesis = FindBestSynthesis(playerPool)
        
        If IsEmpty(bestSynthesis) Then
            synthesisLog = synthesisLog & "第" & iteration & "轮: 无法继续合成，模拟结束。" & vbCrLf
            Exit Do
        End If
        
        ' 记录合成信息
        synthesisLog = synthesisLog & "第" & iteration & "轮: " & _
                      bestSynthesis(0) & " + " & bestSynthesis(1) & " -> " & _
                      bestSynthesis(2) & " (" & bestSynthesis(3) & ")" & vbCrLf
        
        ' 执行合成
        Dim dummyStats As Object
        Set dummyStats = CreateObject("Scripting.Dictionary")
        Call ExecuteSynthesis(playerPool, bestSynthesis, dummyStats)
        
        ' 每5轮显示一次魔法池状态
        If iteration Mod 5 = 0 Then
            synthesisLog = synthesisLog & "  当前魔法池: " & GetPoolSummary(playerPool) & vbCrLf
        End If
        
        synthesisLog = synthesisLog & vbCrLf
    Loop
    
    synthesisLog = synthesisLog & vbCrLf & "最终魔法池:" & vbCrLf
    synthesisLog = synthesisLog & GetDetailedPoolSummary(playerPool)
    
    ' 显示日志（分段显示，避免消息框过长）
    Dim logParts As Variant
    logParts = SplitLongString(synthesisLog, 1000)
    
    Dim i As Integer
    For i = LBound(logParts) To UBound(logParts)
        MsgBox logParts(i), vbInformation, "模拟日志 (" & (i + 1) & "/" & (UBound(logParts) + 1) & ")"
    Next i
End Sub

' 获取魔法池摘要
Private Function GetPoolSummary(playerPool As Object) As String
    Dim summary As String
    summary = ""
    
    Dim tierCounts(1 To 6) As Long
    Dim key As Variant
    For Each key In playerPool.Keys
        Dim tier As Integer
        tier = GetMagicTier(CStr(key))
        If tier >= 1 And tier <= 6 Then
            tierCounts(tier) = tierCounts(tier) + playerPool(key)
        End If
    Next key
    
    Dim i As Integer
    For i = 1 To 6
        If tierCounts(i) > 0 Then
            If summary <> "" Then summary = summary & ", "
            summary = summary & "T" & i & ":" & tierCounts(i)
        End If
    Next i
    
    GetPoolSummary = summary
End Function

' 获取详细的魔法池摘要
Private Function GetDetailedPoolSummary(playerPool As Object) As String
    Dim summary As String
    summary = ""
    
    Dim key As Variant
    For Each key In playerPool.Keys
        summary = summary & key & " x" & playerPool(key) & vbCrLf
    Next key
    
    GetDetailedPoolSummary = summary
End Function

' 分割长字符串
Private Function SplitLongString(longStr As String, maxLength As Integer) As Variant
    Dim parts() As String
    Dim partCount As Integer
    partCount = 0
    
    Dim currentPos As Integer
    currentPos = 1
    
    Do While currentPos <= Len(longStr)
        ReDim Preserve parts(partCount)
        
        Dim endPos As Integer
        endPos = currentPos + maxLength - 1
        If endPos > Len(longStr) Then endPos = Len(longStr)
        
        ' 尝试在换行符处分割
        Dim lastNewLine As Integer
        lastNewLine = InStrRev(Mid(longStr, currentPos, endPos - currentPos + 1), vbCrLf)
        If lastNewLine > 0 And endPos < Len(longStr) Then
            endPos = currentPos + lastNewLine - 1
        End If
        
        parts(partCount) = Mid(longStr, currentPos, endPos - currentPos + 1)
        partCount = partCount + 1
        currentPos = endPos + 1
    Loop
    
    SplitLongString = parts
End Function
