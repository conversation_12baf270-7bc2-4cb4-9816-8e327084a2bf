Option Explicit

' Helper: create scripting dictionary without needing reference
Private Function NewDict() As Object
    Set NewDict = CreateObject("Scripting.Dictionary")
End Function


' --- 全局变量，用于存储从Excel加载的规则，避免重复读取 ---
Private g_wsRules As Worksheet
Private g_MagicRules As Object
Private g_TierTags As Object

' --- 主入口：运行完整模拟 ---
Public Sub RunFullSimulation()
    On Error GoTo ErrorHandle

    ' 初始化
    Dim wsSim As Worksheet
    Set wsSim = ThisWorkbook.Sheets("魔法模拟器")
    
    ' Load rules into memory
    Call LoadRulesIntoMemory

    ' 举例：运行1000次试验
    Dim iterations As Long: iterations = 1000
    Dim aggregatedResults As Object
    Set aggregatedResults = NewDict()
    
    Dim i As Long
    For i = 1 To iterations
        Dim res As Object
        Set res = SimulateSinglePlayerLifecycle()
        
        Dim k As Variant
        For Each k In res.Keys
            If aggregatedResults.Exists(k) Then
                aggregatedResults(k) = aggregatedResults(k) + res(k)
            Else
                aggregatedResults.Add k, res(k)
            End If
        Next k
    Next i
    
    ' 输出结果
    Call OutputAggregatedResults(aggregatedResults, iterations)
    
    Exit Sub
ErrorHandle:
    MsgBox "RunFullSimulation 错误: " & Err.Number & " - " & Err.Description, vbCritical
End Sub

' --- 从工作表加载合成规则到内存（全局变量 g_MagicRules, g_TierTags） ---
Public Sub LoadRulesIntoMemory()
    On Error GoTo ErrorHandler
    
    Dim wsRules As Worksheet
    Set wsRules = ThisWorkbook.Sheets("魔法合成逻辑_v4_Pure_Fix")
    Set g_wsRules = wsRules
    
    Set g_MagicRules = NewDict()
    Set g_TierTags = NewDict()
    
    ' 假设规则表结构：每一行一条魔法，列包含魔法名、标签、等级、合成结果等
    ' 这里的实现示例基于你原始模块里表格的结构，请根据你表格实际列调整索引
    Dim r As Long: r = 14 ' 假设从14行开始有规则
    Do While Trim(wsRules.Cells(r, 2).Value) <> ""
        Dim magicName As String
        magicName = Trim(wsRules.Cells(r, 2).Value)
        If magicName = "" Then Exit Do
        
        Dim tag As String
        tag = Trim(wsRules.Cells(r, 3).Value) ' 假设标签在C列
        Dim tier As Integer
        tier = Val(wsRules.Cells(r, 1).Value) ' 假设等级在A列
        
        ' 存入 g_MagicRules：Key = 魔法名称, Item = Array(tag, tier, ...可扩展)
        If Not g_MagicRules.Exists(magicName) Then
            Dim info As Object
            Set info = NewDict()
            info.Add "tag", tag
            info.Add "tier", tier
            g_MagicRules.Add magicName, info
        End If
        
        ' 存入 g_TierTags：Key = tier, Item = Array of tags
        If Not g_TierTags.Exists(tier) Then
            Dim tarr() As String
            ReDim tarr(0)
            tarr(0) = tag
            g_TierTags.Add tier, tarr
        Else
            Dim arr As Variant
            arr = g_TierTags(tier)
            ' 判断是否已有该tag
            Dim existsTag As Boolean: existsTag = False
            Dim ii As Long
            For ii = LBound(arr) To UBound(arr)
                If arr(ii) = tag Then
                    existsTag = True
                    Exit For
                End If
            Next ii
            If Not existsTag Then
                ReDim Preserve arr(UBound(arr) + 1)
                arr(UBound(arr)) = tag
                g_TierTags(tier) = arr
            End If
        End If
        
        r = r + 1
    Loop

    Exit Sub
ErrorHandler:
    MsgBox "LoadRulesIntoMemory 错误: " & Err.Number & " - " & Err.Description, vbCritical
End Sub

' -------------------------
' 模拟单个玩家的生命周期（示例）
' 返回：Object (Dictionary)，Key=magicName, Value=数量（最终玩家拥有的魔法统计）
' -------------------------
Public Function SimulateSinglePlayerLifecycle() As Object
    Dim playerPool As Object
    Set playerPool = NewDict() ' Key: magicName, Item: count
    
    ' 1. 初始化玩家的魔法池 (这里可以根据需要修改初始魔法)
    ' 示例：给玩家10个随机的T1魔法
    Dim initialMagic As Variant
    Dim t1Tags As Variant: t1Tags = g_TierTags(1)
    Dim i As Integer
    Randomize
    For i = 1 To 10
        Dim randomTag As String
        randomTag = t1Tags(Int((UBound(t1Tags) + 1) * Rnd))
        initialMagic = randomTag & "1"
        If playerPool.Exists(initialMagic) Then
            playerPool(initialMagic) = playerPool(initialMagic) + 1
        Else
            playerPool.Add initialMagic, 1
        End If
    Next i

    ' 2. 循环合成，直到无法再合成
    Dim canSynthesize As Boolean
    Dim possibleCombos As Collection
    Dim tierNum As Integer
    Dim magicName As String
    
    Do
        canSynthesize = False
        Set possibleCombos = New Collection
        
        ' 遍历玩家池，找出可合成的组合（示例：任意两个相同T1合成T2）
        Dim key As Variant
        For Each key In playerPool.Keys
            Dim cnt As Long
            cnt = playerPool(key)
            If cnt >= 2 Then
                ' 假设规则：xx1 + xx1 -> xx2
                If right(CStr(key), 1) = "1" Then
                    Dim newMagic As String
                    newMagic = left(CStr(key), Len(CStr(key)) - 1) & "2"
                    possibleCombos.Add Array(CStr(key), CStr(key), newMagic)
                End If
            End If
        Next key
        
        ' 如果有可合成项，执行合成（示例每次合成一项）
        If possibleCombos.Count > 0 Then
            canSynthesize = True
            Dim combo As Variant
            Set combo = possibleCombos(1)
            Dim a As String, b As String, res As String
            a = combo(0): b = combo(1): res = combo(2)
            
            ' 扣除素材
            playerPool(a) = playerPool(a) - 2
            If playerPool(a) <= 0 Then playerPool.Remove a
            
            ' 增加结果
            If playerPool.Exists(res) Then
                playerPool(res) = playerPool(res) + 1
            Else
                playerPool.Add res, 1
            End If
        End If
    Loop While canSynthesize

    Set SimulateSinglePlayerLifecycle = playerPool
End Function

' -------------------------
' 将聚合结果输出到工作表（演示）
' aggregatedResults: Object(Dictionary) Key=magicName Value=sumCount
' iterations: Long 模拟次数
' -------------------------
Public Sub OutputAggregatedResults(aggregatedResults As Object, iterations As Long)
    On Error GoTo ErrorHandler
    Dim wsOut As Worksheet
    Set wsOut = ThisWorkbook.Sheets("魔法模拟器")
    
    Dim r As Long: r = 4
    wsOut.Range("H4:K100").ClearContents
    
    Dim k As Variant
    For Each k In aggregatedResults.Keys
        wsOut.Cells(r, 8).Value = k ' H列
        wsOut.Cells(r, 9).Value = aggregatedResults(k) ' I列
        wsOut.Cells(r, 10).Value = aggregatedResults(k) / iterations ' J列 平均
        r = r + 1
    Next k

    Exit Sub
ErrorHandler:
    MsgBox "OutputAggregatedResults 错误: " & Err.Number & " - " & Err.Description, vbCritical
End Sub

