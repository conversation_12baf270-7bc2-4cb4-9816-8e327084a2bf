Option Explicit

'==============================================================================
' 简单修复脚本
' 独立的修复工具，不依赖其他模块
'==============================================================================

' 简单的一键修复
Public Sub SimpleOneClickFix()
    On Error GoTo ErrorHandler
    
    MsgBox "开始简单修复..." & vbCrLf & vbCrLf & _
           "这将修复合成规则表中的魔法名称格式问题。", vbInformation
    
    ' 修复规则格式
    Call SimpleFixRuleFormat
    
    ' 显示修复结果
    MsgBox "修复完成！" & vbCrLf & vbCrLf & _
           "现在可以重新运行模拟器测试。" & vbCrLf & vbCrLf & _
           "建议运行: QuickOptimizedSimulation", vbInformation
    
    Exit Sub
    
ErrorHandler:
    MsgBox "修复过程中发生错误: " & Err.Description, vbCritical
End Sub

' 修复合成规则格式
Public Sub SimpleFixRuleFormat()
    On Error GoTo ErrorHandler
    
    Dim wsRules As Worksheet
    Set wsRules = ThisWorkbook.Sheets("合成规则")
    
    Dim lastRow As Long
    lastRow = wsRules.Cells(wsRules.Rows.Count, 1).End(xlUp).Row
    
    Dim fixCount As Integer
    fixCount = 0
    
    Dim fixLog As String
    fixLog = "修复日志:" & vbCrLf & vbCrLf
    
    Dim i As Long
    For i = 2 To lastRow
        Dim resultMagic As String
        resultMagic = Trim(wsRules.Cells(i, 2).Value)
        
        If resultMagic <> "" Then
            ' 检查是否需要添加数字后缀
            Dim lastChar As String
            If Len(resultMagic) > 0 Then
                lastChar = Right(resultMagic, 1)
                
                ' 如果最后一个字符不是数字，添加"1"
                If Not IsNumeric(lastChar) Then
                    Dim newName As String
                    newName = resultMagic & "1"
                    wsRules.Cells(i, 2).Value = newName
                    fixLog = fixLog & "第" & i & "行: " & resultMagic & " -> " & newName & vbCrLf
                    fixCount = fixCount + 1
                End If
            End If
        End If
    Next i
    
    If fixCount > 0 Then
        fixLog = fixLog & vbCrLf & "总共修复了 " & fixCount & " 个魔法名称。"
        MsgBox fixLog, vbInformation, "修复完成"
    Else
        MsgBox "没有发现需要修复的魔法名称格式。", vbInformation, "修复结果"
    End If
    
    Exit Sub
    
ErrorHandler:
    MsgBox "修复规则格式时发生错误: " & Err.Description, vbCritical
End Sub

' 检查合成规则表
Public Sub CheckRulesTable()
    On Error GoTo ErrorHandler
    
    Dim wsRules As Worksheet
    Set wsRules = ThisWorkbook.Sheets("合成规则")
    
    Dim lastRow As Long
    lastRow = wsRules.Cells(wsRules.Rows.Count, 1).End(xlUp).Row
    
    Dim report As String
    report = "=== 合成规则表检查报告 ===" & vbCrLf & vbCrLf
    report = report & "总规则数量: " & (lastRow - 1) & " 条" & vbCrLf & vbCrLf
    
    ' 按等级统计
    Dim tierCounts As Object
    Set tierCounts = CreateObject("Scripting.Dictionary")
    
    Dim i As Long
    For i = 2 To lastRow
        Dim tierTransition As String
        tierTransition = Trim(wsRules.Cells(i, 3).Value)
        
        If tierTransition <> "" Then
            If tierCounts.Exists(tierTransition) Then
                tierCounts(tierTransition) = tierCounts(tierTransition) + 1
            Else
                tierCounts.Add tierTransition, 1
            End If
        End If
    Next i
    
    report = report & "按等级统计:" & vbCrLf
    Dim key As Variant
    For Each key In tierCounts.Keys
        report = report & "  " & key & ": " & tierCounts(key) & " 条" & vbCrLf
    Next key
    
    report = report & vbCrLf & "高等级规则示例:" & vbCrLf
    
    ' 显示一些高等级规则
    For i = 2 To lastRow
        Dim comboKey As String
        Dim resultMagic As String
        tierTransition = Trim(wsRules.Cells(i, 3).Value)
        
        If InStr(tierTransition, "Tier 3") > 0 Or InStr(tierTransition, "Tier 4") > 0 Or InStr(tierTransition, "Tier 5") > 0 Then
            comboKey = Trim(wsRules.Cells(i, 1).Value)
            resultMagic = Trim(wsRules.Cells(i, 2).Value)
            report = report & "  " & comboKey & " -> " & resultMagic & " (" & tierTransition & ")" & vbCrLf
        End If
    Next i
    
    MsgBox report, vbInformation, "规则表检查"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "检查规则表时发生错误: " & Err.Description, vbCritical
End Sub

' 检查魔法池
Public Sub CheckMagicPool()
    On Error GoTo ErrorHandler
    
    Dim wsSim As Worksheet
    Set wsSim = ThisWorkbook.Sheets("魔法模拟器")
    
    Dim magicCount As Long
    magicCount = 0
    
    Dim magicList As String
    magicList = "魔法池内容:" & vbCrLf & vbCrLf
    
    ' 统计魔法
    Dim magicStats As Object
    Set magicStats = CreateObject("Scripting.Dictionary")
    
    Dim cell As Range
    For Each cell In wsSim.Range("H4:J30")
        If Trim(cell.Value) <> "" Then
            Dim magicName As String
            magicName = Trim(cell.Value)
            magicCount = magicCount + 1
            
            If magicStats.Exists(magicName) Then
                magicStats(magicName) = magicStats(magicName) + 1
            Else
                magicStats.Add magicName, 1
            End If
        End If
    Next cell
    
    If magicCount > 0 Then
        magicList = magicList & "总魔法数量: " & magicCount & " 个" & vbCrLf
        magicList = magicList & "魔法种类: " & magicStats.Count & " 种" & vbCrLf & vbCrLf
        
        magicList = magicList & "详细列表:" & vbCrLf
        Dim key As Variant
        For Each key In magicStats.Keys
            magicList = magicList & "  " & key & " x" & magicStats(key) & vbCrLf
        Next key
    Else
        magicList = magicList & "❌ 魔法池为空！" & vbCrLf & vbCrLf & _
                   "请在H4:J30区域设置初始魔法。"
    End If
    
    MsgBox magicList, vbInformation, "魔法池检查"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "检查魔法池时发生错误: " & Err.Description, vbCritical
End Sub

' 完整系统检查
Public Sub FullSystemCheck()
    On Error GoTo ErrorHandler
    
    MsgBox "开始完整系统检查...", vbInformation
    
    ' 1. 检查工作表
    Dim sheetsOK As Boolean
    sheetsOK = True
    
    Dim requiredSheets As Variant
    requiredSheets = Array("合成规则", "统计数据", "魔法模拟器")
    
    Dim sheetReport As String
    sheetReport = "工作表检查:" & vbCrLf
    
    Dim i As Integer
    For i = LBound(requiredSheets) To UBound(requiredSheets)
        Dim ws As Worksheet
        On Error Resume Next
        Set ws = ThisWorkbook.Sheets(requiredSheets(i))
        On Error GoTo ErrorHandler
        
        If ws Is Nothing Then
            sheetReport = sheetReport & "  ❌ " & requiredSheets(i) & vbCrLf
            sheetsOK = False
        Else
            sheetReport = sheetReport & "  ✅ " & requiredSheets(i) & vbCrLf
        End If
        Set ws = Nothing
    Next i
    
    MsgBox sheetReport, vbInformation, "工作表检查"
    
    If sheetsOK Then
        ' 2. 检查合成规则
        Call CheckRulesTable
        
        ' 3. 检查魔法池
        Call CheckMagicPool
        
        MsgBox "系统检查完成！" & vbCrLf & vbCrLf & _
               "如果发现问题，请运行 SimpleOneClickFix 进行修复。", vbInformation
    Else
        MsgBox "❌ 系统检查失败！" & vbCrLf & vbCrLf & _
               "缺少必需的工作表，请检查Excel文件。", vbExclamation
    End If
    
    Exit Sub
    
ErrorHandler:
    MsgBox "系统检查时发生错误: " & Err.Description, vbCritical
End Sub

' 显示使用指南
Public Sub ShowSimpleGuide()
    Dim guide As String
    guide = "=== 简单修复工具使用指南 ===" & vbCrLf & vbCrLf & _
           "【问题诊断】" & vbCrLf & _
           "• FullSystemCheck - 完整系统检查" & vbCrLf & _
           "• CheckRulesTable - 检查合成规则表" & vbCrLf & _
           "• CheckMagicPool - 检查魔法池" & vbCrLf & vbCrLf & _
           "【问题修复】" & vbCrLf & _
           "• SimpleOneClickFix - 一键修复（推荐）" & vbCrLf & _
           "• SimpleFixRuleFormat - 只修复规则格式" & vbCrLf & vbCrLf & _
           "【使用流程】" & vbCrLf & _
           "1. 运行 FullSystemCheck 检查问题" & vbCrLf & _
           "2. 运行 SimpleOneClickFix 修复问题" & vbCrLf & _
           "3. 运行 QuickOptimizedSimulation 测试" & vbCrLf & vbCrLf & _
           "【注意事项】" & vbCrLf & _
           "• 修复会直接修改Excel中的数据" & vbCrLf & _
           "• 建议先备份重要数据" & vbCrLf & _
           "• 修复后需要重新运行模拟器"
    
    MsgBox guide, vbInformation, "使用指南"
End Sub
