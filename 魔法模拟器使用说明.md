# 高级魔法合成模拟器 v1.0 使用说明

## 概述
这是一个完全自动化的魔法合成模拟器，能够模拟玩家从初始T1魔法开始，通过智能合成策略，最终获得T6魔法的完整过程。

## 主要功能

### 1. 完全自动化模拟
- 自动初始化玩家魔法池
- 智能选择最优合成路径
- 自动执行合成直到获得T6魔法或无法继续

### 2. 批量统计分析
- 支持大批量模拟（建议1000-10000次）
- 详细的统计数据收集
- 成功率和效率分析

### 3. 智能合成策略
- 优先合成更高等级魔法（T5->T6 > T4->T5 > ... > T1->T2）
- 自动避免无效合成（相同标签）
- 高效的资源利用

## 使用方法

### 主要函数

#### `RunAdvancedMagicSimulation()`
**主入口函数**
- 运行完整的批量模拟
- 会提示输入模拟次数和初始魔法数量
- 自动输出详细统计结果到"统计数据"工作表

**使用步骤：**
1. 在VBA编辑器中运行此函数
2. 输入模拟次数（建议1000-10000）
3. 输入每个玩家的初始T1魔法数量（建议20-50）
4. 等待模拟完成
5. 查看"统计数据"工作表中的结果

#### `QuickTest()`
**快速测试函数**
- 运行10次模拟进行快速测试
- 用于验证系统是否正常工作

#### `ShowSynthesisRules()`
**显示合成规则**
- 显示已加载的合成规则数量
- 预览前10条合成规则
- 用于调试和验证规则加载

#### `CreatePresetMagicPool()`
**创建预设魔法池**
- 在"魔法模拟器"工作表中创建平衡的T1魔法池
- 每种T1魔法各1个，共32个魔法

#### `AnalyzeMagicPool()`
**分析当前魔法池**
- 分析"魔法模拟器"工作表中当前的魔法池
- 显示各等级魔法数量和可能的合成数量

## 输出结果说明

模拟结果会输出到"统计数据"工作表，包含以下信息：

### 基本统计
- **模拟次数**: 总共运行的模拟次数
- **成功获得T6魔法次数**: 成功合成到T6魔法的次数
- **成功率**: 成功率百分比
- **平均合成轮数**: 平均需要多少轮合成

### 合成阶级统计
- **阶级转换**: 如"T1->T2", "T2->T3"等
- **总次数**: 该阶级转换的总发生次数
- **平均次数**: 每次模拟的平均发生次数
- **占比**: 在所有合成中的占比

### 最终魔法池统计
- **魔法名称**: 最终剩余的魔法名称
- **总数量**: 所有模拟中该魔法的总数量
- **平均数量**: 每次模拟的平均数量
- **等级**: 魔法的等级（T1-T6）

## 系统架构

### 核心组件
1. **合成规则引擎**: 从Excel加载并管理所有合成规则
2. **魔法池管理**: 高效的魔法存储和操作
3. **智能合成算法**: 自动选择最优合成路径
4. **统计分析系统**: 详细的数据收集和分析

### 数据结构
- 使用Dictionary对象实现高效的键值对存储
- 合成规则预加载到内存，避免重复读取Excel
- 智能的优先级算法确保合成效率

## 性能特点

### 高性能设计
- 内存中的规则缓存
- 高效的数据结构
- 优化的合成算法

### 可扩展性
- 易于修改合成规则
- 支持不同的初始化策略
- 灵活的统计输出格式

## 注意事项

1. **Excel工作表要求**:
   - 必须存在"合成规则"工作表
   - 必须存在"统计数据"工作表
   - 必须存在"魔法模拟器"工作表（用于某些功能）

2. **性能建议**:
   - 大批量模拟时建议关闭Excel的自动计算
   - 模拟过程中避免操作Excel界面
   - 建议在性能较好的计算机上运行大批量模拟

3. **数据备份**:
   - 模拟会清空"统计数据"工作表的现有数据
   - 重要数据请提前备份

## 故障排除

### 常见问题
1. **"未找到工作表"错误**: 确保所需的工作表存在
2. **"合成规则加载失败"**: 检查"合成规则"工作表的格式
3. **模拟运行缓慢**: 减少模拟次数或初始魔法数量

### 调试功能
- 使用`QuickTest()`进行快速测试
- 使用`ShowSynthesisRules()`检查规则加载
- 使用`AnalyzeMagicPool()`分析魔法池状态

## 版本信息
- **版本**: v1.0
- **开发日期**: 2025年8月
- **兼容性**: Excel 2016及以上版本
- **依赖**: 需要启用VBA宏功能

## 技术支持
如遇到问题，请检查：
1. VBA宏是否已启用
2. 所需工作表是否存在且格式正确
3. Excel版本是否支持Dictionary对象
