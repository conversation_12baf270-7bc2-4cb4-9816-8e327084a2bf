Option Explicit

'==============================================================================
' 魔法模拟器测试脚本
' 用于验证高级魔法合成模拟器的功能
'==============================================================================

' 主测试函数
Public Sub RunAllTests()
    On Error GoTo ErrorHandler
    
    Application.ScreenUpdating = False
    Application.EnableEvents = False
    
    Dim testResults As String
    testResults = "魔法模拟器测试结果:" & vbCrLf & vbCrLf
    
    ' 测试1: 系统初始化
    testResults = testResults & "测试1: 系统初始化... "
    If TestSystemInitialization() Then
        testResults = testResults & "通过" & vbCrLf
    Else
        testResults = testResults & "失败" & vbCrLf
    End If
    
    ' 测试2: 合成规则加载
    testResults = testResults & "测试2: 合成规则加载... "
    If TestRulesLoading() Then
        testResults = testResults & "通过" & vbCrLf
    Else
        testResults = testResults & "失败" & vbCrLf
    End If
    
    ' 测试3: 魔法等级识别
    testResults = testResults & "测试3: 魔法等级识别... "
    If TestMagicTierRecognition() Then
        testResults = testResults & "通过" & vbCrLf
    Else
        testResults = testResults & "失败" & vbCrLf
    End If
    
    ' 测试4: 单次模拟
    testResults = testResults & "测试4: 单次模拟... "
    If TestSingleSimulation() Then
        testResults = testResults & "通过" & vbCrLf
    Else
        testResults = testResults & "失败" & vbCrLf
    End If
    
    ' 测试5: 批量模拟
    testResults = testResults & "测试5: 批量模拟... "
    If TestBatchSimulation() Then
        testResults = testResults & "通过" & vbCrLf
    Else
        testResults = testResults & "失败" & vbCrLf
    End If
    
    Application.ScreenUpdating = True
    Application.EnableEvents = True
    
    MsgBox testResults, vbInformation, "测试完成"
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    Application.EnableEvents = True
    MsgBox "测试过程中发生错误: " & Err.Description, vbCritical
End Sub

' 测试系统初始化
Private Function TestSystemInitialization() As Boolean
    On Error GoTo ErrorHandler
    
    ' 尝试初始化系统
    Call InitializeSimulationSystem
    
    ' 检查全局变量是否正确初始化
    If g_SynthesisRules Is Nothing Then GoTo ErrorHandler
    If g_MagicTierMap Is Nothing Then GoTo ErrorHandler
    If g_TierPriority Is Nothing Then GoTo ErrorHandler
    
    ' 检查优先级设置
    If g_TierPriority.Count <> 5 Then GoTo ErrorHandler
    If g_TierPriority("T5->T6") <> 5 Then GoTo ErrorHandler
    
    TestSystemInitialization = True
    Exit Function
    
ErrorHandler:
    TestSystemInitialization = False
End Function

' 测试合成规则加载
Private Function TestRulesLoading() As Boolean
    On Error GoTo ErrorHandler
    
    Call InitializeSimulationSystem
    
    ' 检查是否加载了合成规则
    If g_SynthesisRules.Count = 0 Then GoTo ErrorHandler
    
    ' 检查一些已知的合成规则
    If Not g_SynthesisRules.Exists("火焰 + 寒冰") Then GoTo ErrorHandler
    If Not g_SynthesisRules.Exists("寒冰 + 火焰") Then GoTo ErrorHandler ' 反向规则
    
    ' 检查规则内容
    Dim ruleInfo As Variant
    ruleInfo = g_SynthesisRules("火焰 + 寒冰")
    If ruleInfo(0) <> "侵蚀1" Then GoTo ErrorHandler
    If ruleInfo(1) <> "Tier 1 -> Tier 2" Then GoTo ErrorHandler
    
    TestRulesLoading = True
    Exit Function
    
ErrorHandler:
    TestRulesLoading = False
End Function

' 测试魔法等级识别
Private Function TestMagicTierRecognition() As Boolean
    On Error GoTo ErrorHandler
    
    Call InitializeSimulationSystem
    
    ' 测试各等级魔法的识别
    If GetMagicTier("火焰1") <> 1 Then GoTo ErrorHandler
    If GetMagicTier("创造1") <> 2 Then GoTo ErrorHandler
    If GetMagicTier("物质1") <> 3 Then GoTo ErrorHandler
    If GetMagicTier("因果1") <> 4 Then GoTo ErrorHandler
    If GetMagicTier("存在1") <> 5 Then GoTo ErrorHandler
    If GetMagicTier("奇点1") <> 6 Then GoTo ErrorHandler
    
    ' 测试标签提取
    If GetMagicTag("火焰1") <> "火焰" Then GoTo ErrorHandler
    If GetMagicTag("创造123") <> "创造" Then GoTo ErrorHandler
    
    TestMagicTierRecognition = True
    Exit Function
    
ErrorHandler:
    TestMagicTierRecognition = False
End Function

' 测试单次模拟
Private Function TestSingleSimulation() As Boolean
    On Error GoTo ErrorHandler
    
    Call InitializeSimulationSystem
    
    ' 运行一次模拟
    Dim result As Object
    Set result = SimulateSinglePlayer(20)
    
    ' 检查返回结果的结构
    If Not result.Exists("FinalPool") Then GoTo ErrorHandler
    If Not result.Exists("SynthesisStats") Then GoTo ErrorHandler
    If Not result.Exists("HasT6") Then GoTo ErrorHandler
    If Not result.Exists("Iterations") Then GoTo ErrorHandler
    
    ' 检查最终魔法池不为空
    Dim finalPool As Object
    Set finalPool = result("FinalPool")
    If finalPool.Count = 0 Then GoTo ErrorHandler
    
    TestSingleSimulation = True
    Exit Function
    
ErrorHandler:
    TestSingleSimulation = False
End Function

' 测试批量模拟
Private Function TestBatchSimulation() As Boolean
    On Error GoTo ErrorHandler
    
    Call InitializeSimulationSystem
    
    ' 运行小批量模拟
    Dim aggregatedResults As Object
    Set aggregatedResults = RunBatchSimulation(5, 20)
    
    ' 检查聚合结果的结构
    If Not aggregatedResults.Exists("FinalPools") Then GoTo ErrorHandler
    If Not aggregatedResults.Exists("SynthesisStats") Then GoTo ErrorHandler
    If Not aggregatedResults.Exists("SuccessCount") Then GoTo ErrorHandler
    If Not aggregatedResults.Exists("TotalIterations") Then GoTo ErrorHandler
    
    ' 检查成功次数在合理范围内
    Dim successCount As Long
    successCount = aggregatedResults("SuccessCount")
    If successCount < 0 Or successCount > 5 Then GoTo ErrorHandler
    
    TestBatchSimulation = True
    Exit Function
    
ErrorHandler:
    TestBatchSimulation = False
End Function

' 创建测试用的魔法池
Public Sub CreateTestMagicPool()
    On Error GoTo ErrorHandler
    
    Dim wsSim As Worksheet
    Set wsSim = ThisWorkbook.Sheets("魔法模拟器")
    
    ' 清空现有内容
    wsSim.Range("H4:J30").ClearContents
    
    ' 创建一个简单的测试魔法池
    wsSim.Range("H4").Value = "火焰1"
    wsSim.Range("H5").Value = "火焰1"
    wsSim.Range("H6").Value = "寒冰1"
    wsSim.Range("H7").Value = "寒冰1"
    wsSim.Range("H8").Value = "风暴1"
    wsSim.Range("H9").Value = "大地1"
    wsSim.Range("H10").Value = "生命1"
    wsSim.Range("H11").Value = "死亡1"
    wsSim.Range("H12").Value = "心灵1"
    wsSim.Range("H13").Value = "灵魂1"
    
    MsgBox "测试魔法池已创建！", vbInformation
    
    Exit Sub
    
ErrorHandler:
    MsgBox "创建测试魔法池时发生错误: " & Err.Description, vbCritical
End Sub

' 验证合成规则的完整性
Public Sub ValidateSynthesisRules()
    On Error GoTo ErrorHandler
    
    Call InitializeSimulationSystem
    
    Dim report As String
    report = "合成规则验证报告:" & vbCrLf & vbCrLf
    report = report & "总规则数量: " & g_SynthesisRules.Count & vbCrLf & vbCrLf
    
    ' 按等级转换统计
    Dim tierTransitions As Object
    Set tierTransitions = CreateObject("Scripting.Dictionary")
    
    Dim key As Variant
    For Each key In g_SynthesisRules.Keys
        Dim ruleInfo As Variant
        ruleInfo = g_SynthesisRules(key)
        Dim transition As String
        transition = ruleInfo(1)
        
        If tierTransitions.Exists(transition) Then
            tierTransitions(transition) = tierTransitions(transition) + 1
        Else
            tierTransitions.Add transition, 1
        End If
    Next key
    
    For Each key In tierTransitions.Keys
        report = report & key & ": " & tierTransitions(key) & " 条规则" & vbCrLf
    Next key
    
    MsgBox report, vbInformation, "规则验证"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "验证合成规则时发生错误: " & Err.Description, vbCritical
End Sub
