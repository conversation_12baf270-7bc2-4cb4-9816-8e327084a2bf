Option Explicit

'==============================================================================
' 魔法模拟器启动脚本
' 提供简单的入口点来启动模拟器系统
'==============================================================================

' 主启动函数 - 推荐使用这个函数开始
Public Sub StartMagicSimulator()
    On Error GoTo ErrorHandler
    
    ' 显示欢迎信息
    Dim welcomeMsg As String
    welcomeMsg = "欢迎使用高级魔法合成模拟器！" & vbCrLf & vbCrLf & _
                "这个模拟器可以：" & vbCrLf & _
                "• 自动模拟玩家的魔法合成过程" & vbCrLf & _
                "• 统计各阶级魔法的合成次数" & vbCrLf & _
                "• 分析成功率和合成效率" & vbCrLf & _
                "• 提供详细的数据分析报告" & vbCrLf & vbCrLf & _
                "点击确定打开控制面板..."
    
    MsgBox welcomeMsg, vbInformation, "魔法模拟器"
    
    ' 启动控制面板
    Call ShowMagicSimulatorControlPanel
    
    Exit Sub
    
ErrorHandler:
    MsgBox "启动模拟器时发生错误: " & Err.Description, vbCritical, "启动错误"
End Sub

' 快速启动 - 直接运行标准模拟
Public Sub QuickStartSimulation()
    On Error GoTo ErrorHandler
    
    Dim result As VbMsgBoxResult
    result = MsgBox("快速启动模拟器" & vbCrLf & vbCrLf & _
                   "将使用默认设置运行1000次模拟，每个玩家20个初始魔法。" & vbCrLf & vbCrLf & _
                   "预计耗时约1-2分钟。" & vbCrLf & vbCrLf & _
                   "确定要开始吗？", _
                   vbYesNo + vbQuestion, "快速启动")
    
    If result = vbYes Then
        Application.ScreenUpdating = False
        
        ' 初始化系统
        Call InitializeSimulationSystem
        
        ' 运行模拟
        Dim aggregatedResults As Object
        Set aggregatedResults = RunBatchSimulation(1000, 20)
        
        ' 输出结果
        Call OutputSimulationResults(aggregatedResults, 1000)
        
        Application.ScreenUpdating = True
        
        ' 显示结果摘要
        Dim summary As String
        summary = "快速模拟完成！" & vbCrLf & vbCrLf & _
                 "成功次数: " & aggregatedResults("SuccessCount") & "/1000" & vbCrLf & _
                 "成功率: " & Format(aggregatedResults("SuccessCount") / 1000, "0.00%") & vbCrLf & _
                 "平均合成轮数: " & Format(aggregatedResults("TotalIterations") / 1000, "0.0") & vbCrLf & vbCrLf & _
                 "详细结果已输出到统计数据工作表。"
        
        MsgBox summary, vbInformation, "快速模拟结果"
    End If
    
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    MsgBox "快速启动时发生错误: " & Err.Description, vbCritical, "启动错误"
End Sub

' 系统检查 - 验证所有必需的工作表和组件
Public Sub CheckSystemRequirements()
    On Error GoTo ErrorHandler
    
    Dim checkResults As String
    checkResults = "系统检查结果:" & vbCrLf & vbCrLf
    
    Dim allOK As Boolean
    allOK = True
    
    ' 检查必需的工作表
    Dim requiredSheets As Variant
    requiredSheets = Array("合成规则", "统计数据", "魔法模拟器")
    
    Dim i As Integer
    For i = LBound(requiredSheets) To UBound(requiredSheets)
        Dim sheetName As String
        sheetName = requiredSheets(i)
        
        Dim ws As Worksheet
        On Error Resume Next
        Set ws = ThisWorkbook.Sheets(sheetName)
        On Error GoTo ErrorHandler
        
        If ws Is Nothing Then
            checkResults = checkResults & "❌ 工作表 '" & sheetName & "' 不存在" & vbCrLf
            allOK = False
        Else
            checkResults = checkResults & "✅ 工作表 '" & sheetName & "' 存在" & vbCrLf
        End If
        Set ws = Nothing
    Next i
    
    checkResults = checkResults & vbCrLf
    
    ' 检查合成规则数据
    On Error Resume Next
    Dim wsRules As Worksheet
    Set wsRules = ThisWorkbook.Sheets("合成规则")
    
    If Not wsRules Is Nothing Then
        Dim lastRow As Long
        lastRow = wsRules.Cells(wsRules.Rows.Count, 1).End(xlUp).Row
        
        If lastRow > 1 Then
            checkResults = checkResults & "✅ 合成规则数据存在 (" & (lastRow - 1) & " 条规则)" & vbCrLf
        Else
            checkResults = checkResults & "❌ 合成规则数据为空" & vbCrLf
            allOK = False
        End If
    End If
    
    On Error GoTo ErrorHandler
    
    ' 检查VBA功能
    checkResults = checkResults & "✅ VBA宏功能正常" & vbCrLf
    checkResults = checkResults & "✅ Dictionary对象支持正常" & vbCrLf
    
    checkResults = checkResults & vbCrLf
    
    If allOK Then
        checkResults = checkResults & "🎉 系统检查通过！可以正常使用模拟器。"
    Else
        checkResults = checkResults & "⚠️ 系统检查发现问题，请修复后再使用。"
    End If
    
    MsgBox checkResults, IIf(allOK, vbInformation, vbExclamation), "系统检查"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "系统检查时发生错误: " & Err.Description, vbCritical, "检查错误"
End Sub

' 创建示例数据 - 为测试创建基本的合成规则数据
Public Sub CreateSampleData()
    On Error GoTo ErrorHandler
    
    Dim result As VbMsgBoxResult
    result = MsgBox("创建示例数据" & vbCrLf & vbCrLf & _
                   "这将在合成规则工作表中创建一些示例数据用于测试。" & vbCrLf & _
                   "注意：这会覆盖现有的合成规则数据！" & vbCrLf & vbCrLf & _
                   "确定要继续吗？", _
                   vbYesNo + vbExclamation, "创建示例数据")
    
    If result = vbNo Then Exit Sub
    
    Dim wsRules As Worksheet
    Set wsRules = ThisWorkbook.Sheets("合成规则")
    
    ' 清空现有数据
    wsRules.Cells.ClearContents
    
    ' 创建标题行
    wsRules.Range("A1").Value = "组合键"
    wsRules.Range("B1").Value = "结果标签"
    wsRules.Range("C1").Value = "合成阶级"
    
    ' 创建一些示例规则
    Dim sampleRules As Variant
    sampleRules = Array( _
        Array("火焰 + 寒冰", "侵蚀1", "Tier 1 -> Tier 2"), _
        Array("火焰 + 风暴", "创造1", "Tier 1 -> Tier 2"), _
        Array("寒冰 + 风暴", "守护1", "Tier 1 -> Tier 2"), _
        Array("创造 + 毁灭", "能量1", "Tier 2 -> Tier 3"), _
        Array("物质 + 能量", "因果1", "Tier 3 -> Tier 4"), _
        Array("因果 + 平衡", "非在1", "Tier 4 -> Tier 5"), _
        Array("存在 + 非在", "奇点1", "Tier 5 -> Tier 6") _
    )
    
    Dim row As Integer
    For row = LBound(sampleRules) To UBound(sampleRules)
        wsRules.Cells(row + 2, 1).Value = sampleRules(row)(0)
        wsRules.Cells(row + 2, 2).Value = sampleRules(row)(1)
        wsRules.Cells(row + 2, 3).Value = sampleRules(row)(2)
    Next row
    
    ' 格式化表格
    With wsRules.Range("A1:C1")
        .Font.Bold = True
        .Interior.Color = RGB(200, 200, 200)
        .Borders.LineStyle = xlContinuous
    End With
    
    wsRules.Columns("A:C").AutoFit
    
    MsgBox "示例数据创建完成！" & vbCrLf & vbCrLf & _
           "已创建 " & (UBound(sampleRules) + 1) & " 条示例合成规则。" & vbCrLf & _
           "现在可以运行模拟器进行测试。", _
           vbInformation, "创建完成"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "创建示例数据时发生错误: " & Err.Description, vbCritical, "创建错误"
End Sub

' 显示使用指南
Public Sub ShowQuickGuide()
    Dim guide As String
    guide = "=== 魔法模拟器快速使用指南 ===" & vbCrLf & vbCrLf & _
           "【第一次使用】" & vbCrLf & _
           "1. 运行 CheckSystemRequirements() 检查系统" & vbCrLf & _
           "2. 如果缺少数据，运行 CreateSampleData() 创建示例" & vbCrLf & _
           "3. 运行 StartMagicSimulator() 开始使用" & vbCrLf & vbCrLf & _
           "【快速测试】" & vbCrLf & _
           "• 运行 QuickStartSimulation() 进行快速测试" & vbCrLf & _
           "• 运行 QuickTest() 验证系统功能" & vbCrLf & vbCrLf & _
           "【主要功能】" & vbCrLf & _
           "• StartMagicSimulator() - 启动完整控制面板" & vbCrLf & _
           "• RunAdvancedMagicSimulation() - 运行完整模拟" & vbCrLf & _
           "• ShowMagicSimulatorControlPanel() - 显示控制面板" & vbCrLf & vbCrLf & _
           "【结果查看】" & vbCrLf & _
           "模拟结果会自动输出到'统计数据'工作表中，" & vbCrLf & _
           "包含成功率、合成次数统计等详细信息。" & vbCrLf & vbCrLf & _
           "如有问题，请先运行系统检查功能。"
    
    MsgBox guide, vbInformation, "使用指南"
End Sub
