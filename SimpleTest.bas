Option Explicit

'==============================================================================
' 简化测试脚本
' 提供基本的功能测试，避免复杂的模块间依赖
'==============================================================================

' 简单的系统测试
Public Sub SimpleSystemTest()
    On Error GoTo ErrorHandler
    
    Dim testResults As String
    testResults = "简单系统测试结果:" & vbCrLf & vbCrLf
    
    ' 测试1: 检查工作表
    testResults = testResults & "测试1: 检查必需工作表... "
    If TestRequiredSheets() Then
        testResults = testResults & "通过" & vbCrLf
    Else
        testResults = testResults & "失败" & vbCrLf
    End If
    
    ' 测试2: 检查合成规则数据
    testResults = testResults & "测试2: 检查合成规则数据... "
    If TestRulesData() Then
        testResults = testResults & "通过" & vbCrLf
    Else
        testResults = testResults & "失败" & vbCrLf
    End If
    
    ' 测试3: 基本功能测试
    testResults = testResults & "测试3: 基本功能测试... "
    If TestBasicFunctions() Then
        testResults = testResults & "通过" & vbCrLf
    Else
        testResults = testResults & "失败" & vbCrLf
    End If
    
    MsgBox testResults, vbInformation, "测试完成"
    Exit Sub
    
ErrorHandler:
    MsgBox "测试过程中发生错误: " & Err.Description, vbCritical
End Sub

' 测试必需的工作表
Private Function TestRequiredSheets() As Boolean
    On Error GoTo ErrorHandler
    
    Dim requiredSheets As Variant
    requiredSheets = Array("合成规则", "统计数据", "魔法模拟器")
    
    Dim i As Integer
    For i = LBound(requiredSheets) To UBound(requiredSheets)
        Dim ws As Worksheet
        On Error Resume Next
        Set ws = ThisWorkbook.Sheets(requiredSheets(i))
        On Error GoTo ErrorHandler
        
        If ws Is Nothing Then
            TestRequiredSheets = False
            Exit Function
        End If
        Set ws = Nothing
    Next i
    
    TestRequiredSheets = True
    Exit Function
    
ErrorHandler:
    TestRequiredSheets = False
End Function

' 测试合成规则数据
Private Function TestRulesData() As Boolean
    On Error GoTo ErrorHandler
    
    Dim wsRules As Worksheet
    Set wsRules = ThisWorkbook.Sheets("合成规则")
    
    Dim lastRow As Long
    lastRow = wsRules.Cells(wsRules.Rows.Count, 1).End(xlUp).Row
    
    If lastRow > 1 Then
        ' 检查数据格式
        Dim testCell As String
        testCell = wsRules.Cells(2, 1).Value
        
        If InStr(testCell, " + ") > 0 Then
            TestRulesData = True
        Else
            TestRulesData = False
        End If
    Else
        TestRulesData = False
    End If
    
    Exit Function
    
ErrorHandler:
    TestRulesData = False
End Function

' 测试基本功能
Private Function TestBasicFunctions() As Boolean
    On Error GoTo ErrorHandler
    
    ' 尝试初始化系统
    Call InitializeSimulationSystem
    
    ' 检查全局变量是否初始化
    If g_SynthesisRules Is Nothing Then GoTo ErrorHandler
    If g_MagicTierMap Is Nothing Then GoTo ErrorHandler
    If g_TierPriority Is Nothing Then GoTo ErrorHandler
    
    ' 检查是否加载了规则
    If g_SynthesisRules.Count = 0 Then GoTo ErrorHandler
    
    TestBasicFunctions = True
    Exit Function
    
ErrorHandler:
    TestBasicFunctions = False
End Function

' 快速功能演示
Public Sub QuickDemo()
    On Error GoTo ErrorHandler
    
    MsgBox "开始快速功能演示..." & vbCrLf & vbCrLf & _
           "这将运行一个简单的模拟演示。", vbInformation
    
    ' 初始化系统
    Call InitializeSimulationSystem
    
    ' 运行小规模模拟
    Dim aggregatedResults As Object
    Set aggregatedResults = RunBatchSimulation(10, 20)
    
    ' 显示结果
    Dim summary As String
    summary = "快速演示完成！" & vbCrLf & vbCrLf & _
             "运行了10次模拟，每次20个初始魔法" & vbCrLf & _
             "成功次数: " & aggregatedResults("SuccessCount") & "/10" & vbCrLf & _
             "成功率: " & Format(aggregatedResults("SuccessCount") / 10, "0.00%") & vbCrLf & _
             "平均合成轮数: " & Format(aggregatedResults("TotalIterations") / 10, "0.0")
    
    MsgBox summary, vbInformation, "演示结果"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "演示过程中发生错误: " & Err.Description, vbCritical
End Sub

' 显示系统状态
Public Sub ShowSystemStatus()
    On Error GoTo ErrorHandler
    
    Dim status As String
    status = "=== 系统状态报告 ===" & vbCrLf & vbCrLf
    
    ' 检查工作表
    status = status & "【工作表状态】" & vbCrLf
    Dim sheets As Variant
    sheets = Array("合成规则", "统计数据", "魔法模拟器")
    
    Dim i As Integer
    For i = LBound(sheets) To UBound(sheets)
        Dim ws As Worksheet
        On Error Resume Next
        Set ws = ThisWorkbook.Sheets(sheets(i))
        On Error GoTo ErrorHandler
        
        If ws Is Nothing Then
            status = status & "❌ " & sheets(i) & vbCrLf
        Else
            status = status & "✅ " & sheets(i) & vbCrLf
        End If
        Set ws = Nothing
    Next i
    
    status = status & vbCrLf
    
    ' 检查合成规则
    status = status & "【合成规则状态】" & vbCrLf
    On Error Resume Next
    Dim wsRules As Worksheet
    Set wsRules = ThisWorkbook.Sheets("合成规则")
    
    If Not wsRules Is Nothing Then
        Dim lastRow As Long
        lastRow = wsRules.Cells(wsRules.Rows.Count, 1).End(xlUp).Row
        status = status & "规则数量: " & (lastRow - 1) & " 条" & vbCrLf
    Else
        status = status & "❌ 无法访问合成规则工作表" & vbCrLf
    End If
    
    On Error GoTo ErrorHandler
    
    status = status & vbCrLf
    
    ' 检查系统初始化状态
    status = status & "【系统初始化状态】" & vbCrLf
    If g_SynthesisRules Is Nothing Then
        status = status & "❌ 系统未初始化" & vbCrLf
    Else
        status = status & "✅ 系统已初始化" & vbCrLf
        status = status & "已加载规则: " & g_SynthesisRules.Count & " 条" & vbCrLf
        status = status & "魔法标签映射: " & g_MagicTierMap.Count & " 个" & vbCrLf
    End If
    
    MsgBox status, vbInformation, "系统状态"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "获取系统状态时发生错误: " & Err.Description, vbCritical
End Sub

' 重置系统
Public Sub ResetSystem()
    On Error Resume Next
    
    Set g_SynthesisRules = Nothing
    Set g_MagicTierMap = Nothing
    Set g_TierPriority = Nothing
    Set g_Statistics = Nothing
    
    MsgBox "系统已重置。下次使用前需要重新初始化。", vbInformation
End Sub

' 验证单个合成规则
Public Sub TestSingleRule()
    On Error GoTo ErrorHandler
    
    ' 初始化系统
    Call InitializeSimulationSystem
    
    ' 测试一个已知的合成规则
    If g_SynthesisRules.Exists("火焰 + 寒冰") Then
        Dim ruleInfo As Variant
        ruleInfo = g_SynthesisRules("火焰 + 寒冰")
        
        MsgBox "测试合成规则:" & vbCrLf & vbCrLf & _
               "输入: 火焰 + 寒冰" & vbCrLf & _
               "输出: " & ruleInfo(0) & vbCrLf & _
               "阶级: " & ruleInfo(1), vbInformation, "规则测试"
    Else
        MsgBox "未找到测试规则 '火焰 + 寒冰'" & vbCrLf & vbCrLf & _
               "请检查合成规则数据是否正确加载。", vbExclamation, "规则测试"
    End If
    
    Exit Sub
    
ErrorHandler:
    MsgBox "测试规则时发生错误: " & Err.Description, vbCritical
End Sub
