Option Explicit

'==============================================================================
' 优化魔法模拟器启动脚本
' 专门针对确定性合成规则和预设魔法池的启动器
'==============================================================================

' 主启动函数 - 使用Excel中预设的魔法池
Public Sub StartOptimizedMagicSimulator()
    On Error GoTo ErrorHandler
    
    ' 显示欢迎信息
    Dim welcomeMsg As String
    welcomeMsg = "欢迎使用优化魔法合成模拟器！" & vbCrLf & vbCrLf & _
                "🎯 专为确定性合成规则优化" & vbCrLf & _
                "📊 直接读取Excel中的预设魔法池" & vbCrLf & _
                "⚡ 高性能批量模拟" & vbCrLf & _
                "📈 详细的统计分析" & vbCrLf & vbCrLf & _
                "系统将自动读取'魔法模拟器'工作表中H4:J30区域的魔法池。" & vbCrLf & vbCrLf & _
                "点击确定开始..."
    
    MsgBox welcomeMsg, vbInformation, "优化魔法模拟器"
    
    ' 直接启动优化模拟
    Call RunOptimizedSimulation
    
    Exit Sub
    
ErrorHandler:
    MsgBox "启动优化模拟器时发生错误: " & Err.Description, vbCritical, "启动错误"
End Sub

' 快速优化模拟 - 使用默认设置
Public Sub QuickOptimizedSimulation()
    On Error GoTo ErrorHandler
    
    ' 初始化系统
    Call InitializeOptimizedSystem
    
    ' 读取魔法池
    Dim initialPool As Object
    Set initialPool = ReadMagicPoolFromExcel()
    
    If initialPool.Count = 0 Then
        MsgBox "魔法池为空！" & vbCrLf & vbCrLf & _
               "请在'魔法模拟器'工作表的H4:J30区域设置初始魔法。", vbExclamation
        Exit Sub
    End If
    
    ' 显示魔法池信息并确认
    Dim poolInfo As String
    poolInfo = "检测到魔法池:" & vbCrLf & GetPoolSummary(initialPool) & vbCrLf & vbCrLf & _
               "将使用默认设置运行1000次快速模拟。" & vbCrLf & vbCrLf & _
               "确定开始吗？"
    
    If MsgBox(poolInfo, vbYesNo + vbQuestion) = vbNo Then Exit Sub
    
    Application.ScreenUpdating = False
    
    ' 运行模拟
    Dim aggregatedResults As Object
    Set aggregatedResults = RunOptimizedBatchSimulation(initialPool, 1000)
    
    ' 输出结果
    Call OutputOptimizedResults(aggregatedResults, 1000)
    
    Application.ScreenUpdating = True
    
    ' 显示结果摘要
    Dim summary As String
    summary = "快速优化模拟完成！" & vbCrLf & vbCrLf & _
             "成功次数: " & aggregatedResults("SuccessCount") & "/1000" & vbCrLf & _
             "成功率: " & Format(aggregatedResults("SuccessCount") / 1000, "0.00%") & vbCrLf & _
             "平均合成轮数: " & Format(aggregatedResults("TotalIterations") / 1000, "0.0") & vbCrLf & vbCrLf & _
             "详细结果已输出到统计数据工作表。"
    
    MsgBox summary, vbInformation, "快速模拟结果"
    
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    MsgBox "快速优化模拟时发生错误: " & Err.Description, vbCritical, "模拟错误"
End Sub

' 分析当前魔法池的合成潜力
Public Sub AnalyzeCurrentMagicPool()
    On Error GoTo ErrorHandler
    
    ' 初始化系统
    Call InitializeOptimizedSystem
    
    ' 读取魔法池
    Dim playerPool As Object
    Set playerPool = ReadMagicPoolFromExcel()
    
    If playerPool.Count = 0 Then
        MsgBox "魔法池为空！" & vbCrLf & vbCrLf & _
               "请在'魔法模拟器'工作表的H4:J30区域设置魔法。", vbExclamation
        Exit Sub
    End If
    
    ' 分析魔法池
    Dim analysis As String
    analysis = "=== 当前魔法池分析 ===" & vbCrLf & vbCrLf
    
    ' 基本统计
    Dim totalMagic As Long
    totalMagic = 0
    Dim key As Variant
    For Each key In playerPool.Keys
        totalMagic = totalMagic + playerPool(key)
    Next key
    
    analysis = analysis & "总魔法数量: " & totalMagic & " 个" & vbCrLf
    analysis = analysis & "魔法种类: " & playerPool.Count & " 种" & vbCrLf & vbCrLf
    
    ' 按等级统计
    analysis = analysis & "【按等级分布】" & vbCrLf
    Dim tierCounts(1 To 6) As Long
    For Each key In playerPool.Keys
        Dim tier As Integer
        tier = GetOptimizedMagicTier(CStr(key))
        If tier >= 1 And tier <= 6 Then
            tierCounts(tier) = tierCounts(tier) + playerPool(key)
        End If
    Next key
    
    Dim i As Integer
    For i = 1 To 6
        If tierCounts(i) > 0 Then
            analysis = analysis & "T" & i & ": " & tierCounts(i) & " 个" & vbCrLf
        End If
    Next i
    
    analysis = analysis & vbCrLf
    
    ' 详细魔法列表
    analysis = analysis & "【详细魔法列表】" & vbCrLf
    For Each key In playerPool.Keys
        analysis = analysis & key & " x" & playerPool(key) & vbCrLf
    Next key
    
    analysis = analysis & vbCrLf
    
    ' 合成潜力分析
    Dim possibleSyntheses As Long
    possibleSyntheses = CountPossibleOptimizedSyntheses(playerPool)
    analysis = analysis & "【合成潜力】" & vbCrLf
    analysis = analysis & "当前可执行的合成组合: " & possibleSyntheses & " 种" & vbCrLf
    
    ' 预测分析
    If possibleSyntheses > 0 Then
        analysis = analysis & "✅ 魔法池具有合成潜力" & vbCrLf
        If tierCounts(1) >= 2 Then
            analysis = analysis & "✅ 可以进行T1->T2合成" & vbCrLf
        End If
        If tierCounts(2) >= 2 Then
            analysis = analysis & "✅ 可以进行T2->T3合成" & vbCrLf
        End If
    Else
        analysis = analysis & "❌ 当前无法进行任何合成" & vbCrLf
    End If
    
    MsgBox analysis, vbInformation, "魔法池分析"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "分析魔法池时发生错误: " & Err.Description, vbCritical, "分析错误"
End Sub

' 计算可能的合成数量
Private Function CountPossibleOptimizedSyntheses(playerPool As Object) As Long
    Dim count As Long
    count = 0
    
    Dim keys As Variant
    keys = playerPool.Keys
    
    Dim i As Integer, j As Integer
    For i = 0 To UBound(keys)
        For j = i To UBound(keys)
            Dim magic1 As String, magic2 As String
            magic1 = CStr(keys(i))
            magic2 = CStr(keys(j))
            
            ' 检查是否有足够的魔法进行合成
            Dim count1 As Long, count2 As Long
            count1 = playerPool(magic1)
            count2 = playerPool(magic2)
            
            Dim canSynthesize As Boolean
            If magic1 = magic2 Then
                canSynthesize = count1 >= 2
            Else
                canSynthesize = count1 >= 1 And count2 >= 1
            End If
            
            If canSynthesize Then
                ' 检查合成规则
                Dim tag1 As String, tag2 As String
                tag1 = GetOptimizedMagicTag(magic1)
                tag2 = GetOptimizedMagicTag(magic2)
                
                ' 相同标签不能合成
                If tag1 <> tag2 Then
                    Dim comboKey As String
                    comboKey = tag1 & " + " & tag2
                    
                    If g_OptimizedRules.Exists(comboKey) Then
                        count = count + 1
                    End If
                End If
            End If
        Next j
    Next i
    
    CountPossibleOptimizedSyntheses = count
End Function

' 单步模拟演示 - 显示每一步的合成过程
Public Sub SingleStepDemo()
    On Error GoTo ErrorHandler
    
    ' 初始化系统
    Call InitializeOptimizedSystem
    
    ' 读取魔法池
    Dim playerPool As Object
    Set playerPool = ReadMagicPoolFromExcel()
    
    If playerPool.Count = 0 Then
        MsgBox "魔法池为空！请先设置初始魔法。", vbExclamation
        Exit Sub
    End If
    
    MsgBox "开始单步演示..." & vbCrLf & vbCrLf & _
           "初始魔法池: " & GetPoolSummary(playerPool), vbInformation
    
    Dim step As Integer
    step = 0
    Dim maxSteps As Integer
    maxSteps = 20
    
    Do While step < maxSteps
        step = step + 1
        
        ' 检查是否已有T6魔法
        If HasOptimizedMagicOfTier(playerPool, 6) Then
            MsgBox "第" & step & "步: 已获得T6魔法！演示成功完成！", vbInformation
            Exit Do
        End If
        
        ' 寻找最佳合成选项
        Dim bestSynthesis As Variant
        bestSynthesis = FindOptimizedBestSynthesis(playerPool)
        
        If IsEmpty(bestSynthesis) Then
            MsgBox "第" & step & "步: 无法继续合成，演示结束。" & vbCrLf & vbCrLf & _
                   "最终魔法池: " & GetPoolSummary(playerPool), vbInformation
            Exit Do
        End If
        
        ' 显示合成信息
        Dim stepInfo As String
        stepInfo = "第" & step & "步合成:" & vbCrLf & vbCrLf & _
                  "材料: " & bestSynthesis(0) & " + " & bestSynthesis(1) & vbCrLf & _
                  "结果: " & bestSynthesis(2) & vbCrLf & _
                  "类型: " & bestSynthesis(3) & vbCrLf & vbCrLf & _
                  "合成前: " & GetPoolSummary(playerPool) & vbCrLf
        
        ' 执行合成
        Dim dummyStats As Object
        Set dummyStats = CreateObject("Scripting.Dictionary")
        Call ExecuteOptimizedSynthesis(playerPool, bestSynthesis, dummyStats)
        
        stepInfo = stepInfo & "合成后: " & GetPoolSummary(playerPool) & vbCrLf & vbCrLf & _
                   "继续下一步？"
        
        If MsgBox(stepInfo, vbYesNo + vbQuestion, "单步演示") = vbNo Then
            Exit Do
        End If
    Loop
    
    MsgBox "单步演示结束。", vbInformation
    
    Exit Sub
    
ErrorHandler:
    MsgBox "单步演示时发生错误: " & Err.Description, vbCritical, "演示错误"
End Sub

' 显示优化模拟器帮助
Public Sub ShowOptimizedHelp()
    Dim helpText As String
    helpText = "=== 优化魔法合成模拟器帮助 ===" & vbCrLf & vbCrLf & _
              "【主要特点】" & vbCrLf & _
              "• 专为确定性合成规则优化（100%成功率）" & vbCrLf & _
              "• 直接读取Excel中预设的魔法池" & vbCrLf & _
              "• 高性能批量模拟" & vbCrLf & _
              "• 智能合成策略（优先高等级合成）" & vbCrLf & vbCrLf & _
              "【使用方法】" & vbCrLf & _
              "1. 在'魔法模拟器'工作表的H4:J30区域设置初始魔法" & vbCrLf & _
              "2. 运行 StartOptimizedMagicSimulator 开始模拟" & vbCrLf & _
              "3. 查看'统计数据'工作表中的结果" & vbCrLf & vbCrLf & _
              "【主要函数】" & vbCrLf & _
              "• StartOptimizedMagicSimulator - 完整模拟" & vbCrLf & _
              "• QuickOptimizedSimulation - 快速模拟(1000次)" & vbCrLf & _
              "• AnalyzeCurrentMagicPool - 分析当前魔法池" & vbCrLf & _
              "• SingleStepDemo - 单步演示合成过程" & vbCrLf & vbCrLf & _
              "【注意事项】" & vbCrLf & _
              "• 确保'合成规则'工作表包含完整的合成规则" & vbCrLf & _
              "• 魔法池中的魔法名称必须与规则匹配" & vbCrLf & _
              "• 相同标签的魔法无法合成"
    
    MsgBox helpText, vbInformation, "优化模拟器帮助"
End Sub

' 测试合成规则加载
Public Sub TestOptimizedRules()
    On Error GoTo ErrorHandler

    ' 初始化系统
    Call InitializeOptimizedSystem

    Dim report As String
    report = "=== 优化合成规则测试报告 ===" & vbCrLf & vbCrLf
    report = report & "总规则数量: " & g_OptimizedRules.Count & vbCrLf & vbCrLf

    ' 测试关键规则
    Dim testRules As Variant
    testRules = Array( _
        Array("火焰 + 寒冰", "应该有T1->T2规则"), _
        Array("创造 + 毁灭", "应该有T2->T3规则"), _
        Array("物质 + 能量", "应该有T3->T4规则"), _
        Array("因果 + 平衡", "应该有T4->T5规则"), _
        Array("存在 + 非在", "应该有T5->T6规则") _
    )

    Dim i As Integer
    For i = LBound(testRules) To UBound(testRules)
        Dim testKey As String
        testKey = testRules(i)(0)

        If g_OptimizedRules.Exists(testKey) Then
            Dim ruleInfo As Variant
            ruleInfo = g_OptimizedRules(testKey)
            report = report & "✅ " & testKey & " -> " & ruleInfo(0) & " (" & ruleInfo(1) & ")" & vbCrLf
        Else
            report = report & "❌ " & testKey & " - 规则不存在" & vbCrLf
        End If
    Next i

    report = report & vbCrLf & "【高等级规则详情】" & vbCrLf

    ' 显示所有T3->T4规则
    Dim key As Variant
    For Each key In g_OptimizedRules.Keys
        Dim ruleInfo As Variant
        ruleInfo = g_OptimizedRules(key)

        If ruleInfo(1) = "Tier 3 -> Tier 4" Or ruleInfo(1) = "Tier 4 -> Tier 5" Or ruleInfo(1) = "Tier 5 -> Tier 6" Then
            report = report & key & " -> " & ruleInfo(0) & " (" & ruleInfo(1) & ")" & vbCrLf
        End If
    Next key

    MsgBox report, vbInformation, "规则测试"

    Exit Sub

ErrorHandler:
    MsgBox "测试规则时发生错误: " & Err.Description, vbCritical
End Sub
