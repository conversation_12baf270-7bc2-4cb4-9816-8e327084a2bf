Option Explicit

'==============================================================================
' 优化魔法合成模拟器 v2.0
' 专门针对确定性合成规则（100%成功率）和预设魔法池的优化版本
'==============================================================================

' 全局变量
Public g_OptimizedRules As Object
Public g_OptimizedTierMap As Object
Public g_OptimizedStats As Object

' 主入口函数 - 从Excel魔法池读取并模拟
Public Sub RunOptimizedSimulation()
    On Error GoTo ErrorHandler
    
    Application.ScreenUpdating = False
    Application.EnableEvents = False
    
    ' 初始化系统
    Call InitializeOptimizedSystem
    
    ' 从Excel读取当前魔法池
    Dim initialPool As Object
    Set initialPool = ReadMagicPoolFromExcel()
    
    If initialPool.Count = 0 Then
        MsgBox "魔法池为空！请先在魔法模拟器工作表中设置初始魔法。", vbExclamation
        GoTo Cleanup
    End If
    
    ' 获取模拟次数
    Dim simulationCount As Long
    simulationCount = InputBox("请输入模拟次数（建议1000-10000）:", "模拟配置", "1000")
    
    If simulationCount <= 0 Then
        MsgBox "模拟次数无效！", vbExclamation
        GoTo Cleanup
    End If
    
    ' 显示初始魔法池信息
    Dim poolInfo As String
    poolInfo = "当前魔法池:" & vbCrLf & GetPoolSummary(initialPool) & vbCrLf & vbCrLf & _
               "将运行 " & simulationCount & " 次模拟" & vbCrLf & vbCrLf & _
               "确定开始吗？"
    
    If MsgBox(poolInfo, vbYesNo + vbQuestion) = vbNo Then
        GoTo Cleanup
    End If
    
    ' 运行批量模拟
    Dim aggregatedResults As Object
    Set aggregatedResults = RunOptimizedBatchSimulation(initialPool, simulationCount)
    
    ' 输出结果
    Call OutputOptimizedResults(aggregatedResults, simulationCount)
    
    ' 显示结果摘要
    Dim summary As String
    summary = "优化模拟完成！" & vbCrLf & vbCrLf & _
             "模拟次数: " & simulationCount & vbCrLf & _
             "成功获得T6次数: " & aggregatedResults("SuccessCount") & vbCrLf & _
             "成功率: " & Format(aggregatedResults("SuccessCount") / simulationCount, "0.00%") & vbCrLf & _
             "平均合成轮数: " & Format(aggregatedResults("TotalIterations") / simulationCount, "0.0") & vbCrLf & vbCrLf & _
             "详细结果已输出到统计数据工作表。"
    
    MsgBox summary, vbInformation, "模拟结果"
    
Cleanup:
    Application.ScreenUpdating = True
    Application.EnableEvents = True
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    Application.EnableEvents = True
    MsgBox "模拟过程中发生错误: " & Err.Description & " (错误号: " & Err.Number & ")", vbCritical
End Sub

' 初始化优化系统
Public Sub InitializeOptimizedSystem()
    Set g_OptimizedRules = CreateObject("Scripting.Dictionary")
    Set g_OptimizedTierMap = CreateObject("Scripting.Dictionary")
    Set g_OptimizedStats = CreateObject("Scripting.Dictionary")
    
    ' 加载合成规则
    Call LoadOptimizedRules
    
    ' 初始化魔法等级映射
    Call InitializeOptimizedTierMap
End Sub

' 从Excel读取魔法池
Private Function ReadMagicPoolFromExcel() As Object
    On Error GoTo ErrorHandler
    
    Dim playerPool As Object
    Set playerPool = CreateObject("Scripting.Dictionary")
    
    Dim wsSim As Worksheet
    Set wsSim = ThisWorkbook.Sheets("魔法模拟器")
    
    ' 读取魔法池区域 H4:J30
    Dim cell As Range
    For Each cell In wsSim.Range("H4:J30")
        If Trim(cell.Value) <> "" Then
            Dim magicName As String
            magicName = Trim(cell.Value)
            
            If playerPool.Exists(magicName) Then
                playerPool(magicName) = playerPool(magicName) + 1
            Else
                playerPool.Add magicName, 1
            End If
        End If
    Next cell
    
    Set ReadMagicPoolFromExcel = playerPool
    Exit Function
    
ErrorHandler:
    Set ReadMagicPoolFromExcel = CreateObject("Scripting.Dictionary")
End Function

' 加载优化的合成规则
Private Sub LoadOptimizedRules()
    On Error GoTo ErrorHandler
    
    Dim wsRules As Worksheet
    Set wsRules = ThisWorkbook.Sheets("合成规则")
    
    Dim lastRow As Long
    lastRow = wsRules.Cells(wsRules.Rows.Count, 1).End(xlUp).Row
    
    Dim i As Long
    For i = 2 To lastRow
        Dim comboKey As String
        Dim resultMagic As String
        Dim tierTransition As String
        
        comboKey = Trim(wsRules.Cells(i, 1).Value)
        resultMagic = Trim(wsRules.Cells(i, 2).Value)
        tierTransition = Trim(wsRules.Cells(i, 3).Value)
        
        If comboKey <> "" And resultMagic <> "" Then
            ' 修正结果魔法名称格式 - 如果没有数字后缀，添加"1"
            Dim correctedResultMagic As String
            correctedResultMagic = resultMagic

            ' 检查是否已经有数字后缀
            Dim hasNumber As Boolean
            hasNumber = False
            Dim lastChar As String
            If Len(resultMagic) > 0 Then
                lastChar = Right(resultMagic, 1)
                hasNumber = IsNumeric(lastChar)
            End If

            ' 如果没有数字后缀，添加"1"
            If Not hasNumber Then
                correctedResultMagic = resultMagic & "1"
            End If

            ' 存储合成规则
            g_OptimizedRules.Add comboKey, Array(correctedResultMagic, tierTransition)

            ' 同时存储反向组合
            Dim reversedKey As String
            reversedKey = GetReversedComboKey(comboKey)
            If reversedKey <> comboKey And Not g_OptimizedRules.Exists(reversedKey) Then
                g_OptimizedRules.Add reversedKey, Array(correctedResultMagic, tierTransition)
            End If
        End If
    Next i
    
    Exit Sub
    
ErrorHandler:
    MsgBox "加载合成规则时发生错误: " & Err.Description, vbCritical
End Sub

' 获取反向组合键
Private Function GetReversedComboKey(comboKey As String) As String
    Dim parts As Variant
    parts = Split(comboKey, " + ")
    If UBound(parts) = 1 Then
        GetReversedComboKey = parts(1) & " + " & parts(0)
    Else
        GetReversedComboKey = comboKey
    End If
End Function

' 初始化魔法等级映射
Private Sub InitializeOptimizedTierMap()
    ' T1魔法标签
    Dim t1Tags As Variant
    t1Tags = Array("火焰", "寒冰", "风暴", "大地", "生命", "死亡", "心灵", "灵魂")
    
    ' T2魔法标签
    Dim t2Tags As Variant
    t2Tags = Array("创造", "毁灭", "守护", "侵蚀", "扭曲", "净化")
    
    ' T3魔法标签
    Dim t3Tags As Variant
    t3Tags = Array("物质", "能量", "精神", "虚空")
    
    ' T4魔法标签
    Dim t4Tags As Variant
    t4Tags = Array("因果", "循环", "平衡")
    
    ' T5魔法标签
    Dim t5Tags As Variant
    t5Tags = Array("存在", "非在")
    
    ' T6魔法标签
    Dim t6Tags As Variant
    t6Tags = Array("奇点")
    
    ' 建立映射
    Call AddOptimizedTagsToTierMap(t1Tags, 1)
    Call AddOptimizedTagsToTierMap(t2Tags, 2)
    Call AddOptimizedTagsToTierMap(t3Tags, 3)
    Call AddOptimizedTagsToTierMap(t4Tags, 4)
    Call AddOptimizedTagsToTierMap(t5Tags, 5)
    Call AddOptimizedTagsToTierMap(t6Tags, 6)
End Sub

' 添加标签到等级映射
Private Sub AddOptimizedTagsToTierMap(tags As Variant, tier As Integer)
    Dim i As Integer
    For i = LBound(tags) To UBound(tags)
        g_OptimizedTierMap.Add tags(i), tier
    Next i
End Sub

' 运行优化的批量模拟
Private Function RunOptimizedBatchSimulation(initialPool As Object, simulationCount As Long) As Object
    Dim aggregatedResults As Object
    Set aggregatedResults = CreateObject("Scripting.Dictionary")
    
    ' 初始化统计
    aggregatedResults.Add "SuccessCount", 0
    aggregatedResults.Add "TotalIterations", 0
    aggregatedResults.Add "SynthesisStats", CreateObject("Scripting.Dictionary")
    aggregatedResults.Add "FinalPools", CreateObject("Scripting.Dictionary")
    
    Dim i As Long
    For i = 1 To simulationCount
        ' 显示进度
        If i Mod 100 = 0 Then
            Application.StatusBar = "优化模拟进度: " & i & "/" & simulationCount
        End If
        
        ' 运行单次模拟
        Dim singleResult As Object
        Set singleResult = SimulateOptimizedSinglePlayer(initialPool)
        
        ' 汇总结果
        Call AggregateOptimizedResults(aggregatedResults, singleResult)
    Next i
    
    Application.StatusBar = False
    Set RunOptimizedBatchSimulation = aggregatedResults
End Function

' 模拟单个玩家（优化版）
Private Function SimulateOptimizedSinglePlayer(initialPool As Object) As Object
    ' 复制初始魔法池
    Dim playerPool As Object
    Set playerPool = CloneDictionary(initialPool)
    
    Dim synthesisStats As Object
    Set synthesisStats = CreateObject("Scripting.Dictionary")
    
    ' 执行合成直到获得T6魔法或无法继续合成
    Dim hasT6Magic As Boolean
    Dim maxIterations As Long
    Dim currentIteration As Long
    
    hasT6Magic = False
    maxIterations = 1000
    currentIteration = 0
    
    Do While Not hasT6Magic And currentIteration < maxIterations
        currentIteration = currentIteration + 1
        
        ' 检查是否已有T6魔法
        If HasOptimizedMagicOfTier(playerPool, 6) Then
            hasT6Magic = True
            Exit Do
        End If
        
        ' 寻找最佳合成选项
        Dim bestSynthesis As Variant
        bestSynthesis = FindOptimizedBestSynthesis(playerPool)
        
        If IsEmpty(bestSynthesis) Then
            Exit Do
        End If
        
        ' 执行合成
        Call ExecuteOptimizedSynthesis(playerPool, bestSynthesis, synthesisStats)
    Loop
    
    ' 返回结果
    Dim result As Object
    Set result = CreateObject("Scripting.Dictionary")
    result.Add "FinalPool", playerPool
    result.Add "SynthesisStats", synthesisStats
    result.Add "HasT6", hasT6Magic
    result.Add "Iterations", currentIteration
    
    Set SimulateOptimizedSinglePlayer = result
End Function

' 复制Dictionary对象
Private Function CloneDictionary(sourceDict As Object) As Object
    Dim newDict As Object
    Set newDict = CreateObject("Scripting.Dictionary")
    
    Dim key As Variant
    For Each key In sourceDict.Keys
        newDict.Add key, sourceDict(key)
    Next key
    
    Set CloneDictionary = newDict
End Function

' 检查是否拥有指定等级的魔法
Private Function HasOptimizedMagicOfTier(playerPool As Object, tier As Integer) As Boolean
    Dim key As Variant
    For Each key In playerPool.Keys
        If GetOptimizedMagicTier(CStr(key)) = tier And playerPool(key) > 0 Then
            HasOptimizedMagicOfTier = True
            Exit Function
        End If
    Next key
    HasOptimizedMagicOfTier = False
End Function

' 获取魔法的等级
Private Function GetOptimizedMagicTier(magicName As String) As Integer
    Dim tag As String
    tag = GetOptimizedMagicTag(magicName)

    If g_OptimizedTierMap.Exists(tag) Then
        GetOptimizedMagicTier = g_OptimizedTierMap(tag)
    Else
        GetOptimizedMagicTier = 0
    End If
End Function

' 从魔法名称提取标签
Private Function GetOptimizedMagicTag(magicName As String) As String
    Dim i As Integer
    For i = Len(magicName) To 1 Step -1
        If Not IsNumeric(Mid(magicName, i, 1)) Then
            GetOptimizedMagicTag = Left(magicName, i)
            Exit Function
        End If
    Next i
    GetOptimizedMagicTag = magicName
End Function

' 寻找最佳合成选项（优化版）
Private Function FindOptimizedBestSynthesis(playerPool As Object) As Variant
    ' 定义合成优先级（数字越大优先级越高）
    Dim tierPriority As Object
    Set tierPriority = CreateObject("Scripting.Dictionary")
    tierPriority.Add "Tier 5 -> Tier 6", 5
    tierPriority.Add "Tier 4 -> Tier 5", 4
    tierPriority.Add "Tier 3 -> Tier 4", 3
    tierPriority.Add "Tier 2 -> Tier 3", 2
    tierPriority.Add "Tier 1 -> Tier 2", 1

    Dim bestSynthesis As Variant
    Dim maxPriority As Integer
    maxPriority = -1

    ' 遍历所有可能的魔法组合
    Dim keys As Variant
    keys = playerPool.Keys

    Dim i As Integer, j As Integer
    For i = 0 To UBound(keys)
        For j = i To UBound(keys)
            Dim magic1 As String, magic2 As String
            magic1 = CStr(keys(i))
            magic2 = CStr(keys(j))

            ' 检查是否有足够的魔法进行合成
            Dim count1 As Long, count2 As Long
            count1 = playerPool(magic1)
            count2 = playerPool(magic2)

            Dim canSynthesize As Boolean
            If magic1 = magic2 Then
                canSynthesize = count1 >= 2
            Else
                canSynthesize = count1 >= 1 And count2 >= 1
            End If

            If canSynthesize Then
                ' 检查合成规则
                Dim tag1 As String, tag2 As String
                tag1 = GetOptimizedMagicTag(magic1)
                tag2 = GetOptimizedMagicTag(magic2)

                ' 相同标签不能合成
                If tag1 <> tag2 Then
                    Dim comboKey As String
                    comboKey = tag1 & " + " & tag2

                    If g_OptimizedRules.Exists(comboKey) Then
                        Dim ruleInfo As Variant
                        ruleInfo = g_OptimizedRules(comboKey)

                        Dim resultMagic As String
                        Dim tierTransition As String
                        resultMagic = ruleInfo(0)
                        tierTransition = ruleInfo(1)

                        ' 计算优先级
                        Dim priority As Integer
                        If tierPriority.Exists(tierTransition) Then
                            priority = tierPriority(tierTransition)
                        Else
                            priority = 0
                        End If

                        ' 选择优先级最高的合成
                        If priority > maxPriority Then
                            maxPriority = priority
                            bestSynthesis = Array(magic1, magic2, resultMagic, tierTransition, priority)
                        End If
                    End If
                End If
            End If
        Next j
    Next i

    If maxPriority = -1 Then
        FindOptimizedBestSynthesis = Empty
    Else
        FindOptimizedBestSynthesis = bestSynthesis
    End If
End Function

' 执行合成（优化版）
Private Sub ExecuteOptimizedSynthesis(playerPool As Object, synthesis As Variant, synthesisStats As Object)
    Dim magic1 As String, magic2 As String, resultMagic As String, tierTransition As String
    magic1 = synthesis(0)
    magic2 = synthesis(1)
    resultMagic = synthesis(2)
    tierTransition = synthesis(3)

    ' 消耗材料
    playerPool(magic1) = playerPool(magic1) - 1
    If playerPool(magic1) <= 0 Then
        playerPool.Remove magic1
    End If

    If magic1 <> magic2 Then
        playerPool(magic2) = playerPool(magic2) - 1
        If playerPool(magic2) <= 0 Then
            playerPool.Remove magic2
        End If
    Else
        ' 相同魔法需要再消耗一个
        playerPool(magic1) = playerPool(magic1) - 1
        If playerPool(magic1) <= 0 Then
            playerPool.Remove magic1
        End If
    End If

    ' 添加结果魔法
    If playerPool.Exists(resultMagic) Then
        playerPool(resultMagic) = playerPool(resultMagic) + 1
    Else
        playerPool.Add resultMagic, 1
    End If

    ' 更新统计
    If synthesisStats.Exists(tierTransition) Then
        synthesisStats(tierTransition) = synthesisStats(tierTransition) + 1
    Else
        synthesisStats.Add tierTransition, 1
    End If
End Sub

' 汇总优化结果
Private Sub AggregateOptimizedResults(aggregatedResults As Object, singleResult As Object)
    Dim finalPool As Object
    Set finalPool = singleResult("FinalPool")

    Dim synthesisStats As Object
    Set synthesisStats = singleResult("SynthesisStats")

    ' 汇总最终魔法池
    Dim finalPools As Object
    Set finalPools = aggregatedResults("FinalPools")

    Dim key As Variant
    For Each key In finalPool.Keys
        If finalPools.Exists(key) Then
            finalPools(key) = finalPools(key) + finalPool(key)
        Else
            finalPools.Add key, finalPool(key)
        End If
    Next key

    ' 汇总合成统计
    Dim aggSynthesisStats As Object
    Set aggSynthesisStats = aggregatedResults("SynthesisStats")

    For Each key In synthesisStats.Keys
        If aggSynthesisStats.Exists(key) Then
            aggSynthesisStats(key) = aggSynthesisStats(key) + synthesisStats(key)
        Else
            aggSynthesisStats.Add key, synthesisStats(key)
        End If
    Next key

    ' 汇总成功率
    If singleResult("HasT6") Then
        aggregatedResults("SuccessCount") = aggregatedResults("SuccessCount") + 1
    End If

    ' 汇总迭代次数
    aggregatedResults("TotalIterations") = aggregatedResults("TotalIterations") + singleResult("Iterations")
End Sub

' 获取魔法池摘要
Private Function GetPoolSummary(playerPool As Object) As String
    Dim summary As String
    summary = ""

    Dim tierCounts(1 To 6) As Long
    Dim key As Variant
    For Each key In playerPool.Keys
        Dim tier As Integer
        tier = GetOptimizedMagicTier(CStr(key))
        If tier >= 1 And tier <= 6 Then
            tierCounts(tier) = tierCounts(tier) + playerPool(key)
        End If
    Next key

    Dim i As Integer
    For i = 1 To 6
        If tierCounts(i) > 0 Then
            If summary <> "" Then summary = summary & ", "
            summary = summary & "T" & i & ":" & tierCounts(i) & "个"
        End If
    Next i

    If summary = "" Then summary = "空"
    GetPoolSummary = summary
End Function

' 输出优化结果
Private Sub OutputOptimizedResults(aggregatedResults As Object, simulationCount As Long)
    On Error GoTo ErrorHandler

    Dim wsStats As Worksheet
    Set wsStats = ThisWorkbook.Sheets("统计数据")

    ' 清空现有数据
    wsStats.Range("A1:J1000").ClearContents

    ' 输出标题
    wsStats.Range("A1").Value = "优化魔法合成模拟结果（确定性合成）"
    wsStats.Range("A2").Value = "模拟次数: " & simulationCount
    wsStats.Range("A3").Value = "成功获得T6魔法次数: " & aggregatedResults("SuccessCount")
    wsStats.Range("A4").Value = "成功率: " & Format(aggregatedResults("SuccessCount") / simulationCount, "0.00%")
    wsStats.Range("A5").Value = "平均合成轮数: " & Format(aggregatedResults("TotalIterations") / simulationCount, "0.0")

    ' 输出合成统计
    wsStats.Range("A7").Value = "合成阶级统计:"
    wsStats.Range("A8").Value = "阶级转换"
    wsStats.Range("B8").Value = "总次数"
    wsStats.Range("C8").Value = "平均次数"
    wsStats.Range("D8").Value = "占比"
    wsStats.Range("E8").Value = "成功模拟中平均次数"

    Dim row As Long
    row = 9

    If aggregatedResults.Exists("SynthesisStats") Then
        Dim synthesisStats As Object
        Set synthesisStats = aggregatedResults("SynthesisStats")

        Dim totalSyntheses As Long
        totalSyntheses = 0

        Dim key As Variant
        For Each key In synthesisStats.Keys
            totalSyntheses = totalSyntheses + synthesisStats(key)
        Next key

        ' 按优先级排序输出
        Dim tierOrder As Variant
        tierOrder = Array("Tier 5 -> Tier 6", "Tier 4 -> Tier 5", "Tier 3 -> Tier 4", "Tier 2 -> Tier 3", "Tier 1 -> Tier 2")

        Dim i As Integer
        For i = LBound(tierOrder) To UBound(tierOrder)
            Dim tierTransition As String
            tierTransition = tierOrder(i)

            If synthesisStats.Exists(tierTransition) Then
                Dim count As Long
                count = synthesisStats(tierTransition)

                wsStats.Cells(row, 1).Value = tierTransition
                wsStats.Cells(row, 2).Value = count
                wsStats.Cells(row, 3).Value = Format(count / simulationCount, "0.0")
                wsStats.Cells(row, 4).Value = Format(count / totalSyntheses, "0.00%")

                If aggregatedResults("SuccessCount") > 0 Then
                    wsStats.Cells(row, 5).Value = Format(count / aggregatedResults("SuccessCount"), "0.0")
                Else
                    wsStats.Cells(row, 5).Value = "N/A"
                End If

                row = row + 1
            End If
        Next i
    End If

    ' 格式化表格
    With wsStats.Range("A8:E8")
        .Font.Bold = True
        .Interior.Color = RGB(200, 200, 200)
        .Borders.LineStyle = xlContinuous
    End With

    wsStats.Columns("A:E").AutoFit

    Exit Sub

ErrorHandler:
    MsgBox "输出结果时发生错误: " & Err.Description, vbCritical
End Sub
