Option Explicit
'---------------------------------------------------------------------------------------------------
' Magic Synthesis Simulator (v7.4 - Fixed Worksheet Protection Issue)
'---------------------------------------------------------------------------------------------------

' --- PUBLIC: Main entry point to create the simulator ---
Public Sub CreateFullSimulator_V7_Robust()
    On Error GoTo ErrorHandler
    Application.ScreenUpdating = False
    Application.EnableEvents = False
    
    Const SIM_SHEET_NAME As String = "魔法模拟器"
    Const RULES_SHEET_NAME As String = "魔法合成逻辑_v4_Pure_Fix"
    
    If Not SheetExists(RULES_SHEET_NAME) Then
        MsgBox "错误: 未找到规则表 '" & RULES_SHEET_NAME & "'。", vbCritical
        GoTo Cleanup
    End If
    
    Dim wsSim As Worksheet
    Set wsSim = SetupSheet(SIM_SHEET_NAME)
    
    If wsSim Is Nothing Then
        MsgBox "无法创建工作表", vbCritical
        GoTo Cleanup
    End If
    
    wsSim.Activate
    
    ' 确保工作表未被保护
    If wsSim.ProtectContents Then
        wsSim.Unprotect
    End If
    
    Call InitializeSimulatorData(wsSim, ThisWorkbook.Sheets(RULES_SHEET_NAME))
    ' UI已经从模板复制，不再需要动态绘制
    ' Call DrawSimulatorUI_Fixed(wsSim)
    
    ' 确保从模板复制的控件能正确绑定到宏
    On Error Resume Next
    wsSim.Shapes("btnAddMagic").OnAction = "AddMagicToPool"
    wsSim.Shapes("btnSynthesize").OnAction = "ExecuteSynthesis"
    wsSim.Shapes("ddlTierSelect").OnAction = "TierSelectionChanged"
    ' 兼容旧版模板可能存在的按钮类型
    wsSim.Buttons("btnAddMagic").OnAction = "AddMagicToPool"
    wsSim.Buttons("btnSynthesize").OnAction = "ExecuteSynthesis"
    On Error GoTo 0
    
Cleanup:
    Application.ScreenUpdating = True
    Application.EnableEvents = True
    If Not wsSim Is Nothing Then
        wsSim.Activate
        MsgBox "全功能魔法合成模拟器已创建！", vbInformation
    End If
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    Application.EnableEvents = True
    MsgBox "创建模拟器时发生错误: " & Err.Description & " (错误号: " & Err.Number & ")", vbCritical
End Sub

' --- DrawSimulatorUI_Fixed 函数已被移除，因为现在使用模板 ---

' ===================================================================================================
' 其他函数保持不变，但需要确保在操作工作表时处理保护状态
' ===================================================================================================

Private Function SetupSheet(sheetName As String) As Worksheet
    On Error GoTo ErrorHandler
    Const TEMPLATE_SHEET_NAME As String = "魔法模板"
    
    Dim wsTemplate As Worksheet
    On Error Resume Next
    Set wsTemplate = ThisWorkbook.Sheets(TEMPLATE_SHEET_NAME)
    On Error GoTo 0
    
    If wsTemplate Is Nothing Then
        MsgBox "错误: 未找到模板工作表 '" & TEMPLATE_SHEET_NAME & "'。请先创建并设计好UI模板。", vbCritical
        Set SetupSheet = Nothing
        Exit Function
    End If
    
    Application.DisplayAlerts = False
    
    ' 删除已存在的同名工作表
    On Error Resume Next
    Dim existingSheet As Worksheet
    Set existingSheet = ThisWorkbook.Sheets(sheetName)
    If Not existingSheet Is Nothing Then
        If existingSheet.ProtectContents Then existingSheet.Unprotect
        existingSheet.Delete
    End If
    On Error GoTo 0
    
    ' 复制模板工作表来创建新的模拟器
    wsTemplate.Copy After:=ThisWorkbook.Sheets(ThisWorkbook.Sheets.Count)
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Sheets(ThisWorkbook.Sheets.Count)
    ws.Name = sheetName
    ws.Visible = xlSheetVisible ' 确保复制出来的工作表是可见的
    
    ' 如果模板是被保护的，取消新表的保护
    If ws.ProtectContents Then
        ws.Unprotect
    End If
    
    Application.DisplayAlerts = True
    Set SetupSheet = ws
    Exit Function

ErrorHandler:
    Application.DisplayAlerts = True
    Set SetupSheet = Nothing
End Function

Private Sub InitializeSimulatorData(wsSim As Worksheet, wsRules As Worksheet)
    On Error GoTo ErrorHandler
    
    ' 确保工作表未被保护
    If wsSim.ProtectContents Then
        wsSim.Unprotect
    End If
    
    Dim allMagicList As Collection: Set allMagicList = New Collection
    Dim i As Integer, j As Integer, k As Integer
    
    For i = 5 To 10
        Dim tags As Variant: tags = Split(CStr(wsRules.Cells(i, 3).Value), ", ")
        Dim totalMagicCount As Long: totalMagicCount = Val(wsRules.Cells(i, 4).Value)
        Dim tagCount As Long: tagCount = (UBound(tags) - LBound(tags) + 1)
        
        If tagCount > 0 And totalMagicCount > 0 Then
            Dim magicPerTag As Long: magicPerTag = totalMagicCount / tagCount
            
            Dim tag As Variant
            For Each tag In tags
                For j = 1 To magicPerTag
                    allMagicList.Add Trim(CStr(tag)) & j
                Next j
            Next tag
        End If
    Next i
    
    For k = 1 To allMagicList.Count
        wsSim.Cells(k, 20).Value = allMagicList(k)
    Next k
    
    If allMagicList.Count > 0 Then
        wsSim.Range(wsSim.Cells(1, 20), wsSim.Cells(allMagicList.Count, 20)).Name = "SourceMagicList"
    End If
    
    wsSim.Columns(20).Hidden = True
    wsSim.Range("S1:S5").Value = Application.Transpose(Array("T1 -> T2", "T2 -> T3", "T3 -> T4", "T4 -> T5", "T5 -> T6"))
    wsSim.Range("S1:S5").Name = "TierTextList"
    wsSim.Range("R1:R5").Value = Application.Transpose(Array(1, 2, 3, 4, 5))
    wsSim.Range("R1:R5").Name = "TierSourceNumList"
    wsSim.Columns("R:S").Hidden = True
    
    wsSim.Range("H4:J30").Name = "MagicPool"
    wsSim.Range("Q1:Q100").Name = "MaterialList"
    wsSim.Columns("Q").Hidden = True
    Exit Sub
    
ErrorHandler:
    MsgBox "初始化数据时出错: " & Err.Description, vbCritical
End Sub

' --- 其他宏函数需要添加工作表保护处理 ---
Public Sub AddMagicToPool()
    On Error GoTo ErrorHandler
    Application.ScreenUpdating = False
    Dim wsSim As Worksheet
    Set wsSim = ThisWorkbook.ActiveSheet
    
    ' 确保工作表未被保护
    If wsSim.ProtectContents Then
        wsSim.Unprotect
    End If
    
    Dim listIndex As Integer
    listIndex = wsSim.Shapes("ddlAllMagic").ControlFormat.Value
    If listIndex = 0 Then Exit Sub
    
    Dim selectedMagic As String
    selectedMagic = wsSim.Range("SourceMagicList").Cells(listIndex, 1).Value
    
    Dim firstEmptyCell As Range
    On Error Resume Next
    Set firstEmptyCell = wsSim.Range("MagicPool").SpecialCells(xlCellTypeBlanks).Cells(1)
    On Error GoTo 0
    
    If firstEmptyCell Is Nothing Then
        MsgBox "魔法池已满！", vbExclamation
    Else
        firstEmptyCell.Value = selectedMagic
    End If
    
    Call TierSelectionChanged
    Application.ScreenUpdating = True
    Exit Sub
ErrorHandler:
    Application.ScreenUpdating = True
    MsgBox "添加魔法时出错: " & Err.Description, vbCritical
End Sub

Public Sub ExecuteSynthesis()
    On Error GoTo ErrorHandler
    Application.ScreenUpdating = False
    Dim wsSim As Worksheet
    Set wsSim = ThisWorkbook.ActiveSheet
    
    ' 确保工作表未被保护
    If wsSim.ProtectContents Then
        wsSim.Unprotect
    End If
    
    Dim material1 As String, material2 As String
    material1 = CStr(wsSim.Range("B13").Value)
    material2 = CStr(wsSim.Range("D13").Value)
    
    If material1 = "" Or material2 = "" Then
        MsgBox "请选择两种魔法作为材料。", vbExclamation
        GoTo Cleanup
    End If
    If material1 = material2 And Application.WorksheetFunction.CountIf(wsSim.Range("MagicPool"), material1) < 2 Then
        MsgBox "材料不足: 你需要至少两个 '" & material1 & "' 才能进行合成。", vbExclamation
        GoTo Cleanup
    End If
    
    Dim resultMagic As String
    resultMagic = CalculateResult(wsSim, material1, material2)
    
    If resultMagic = "Error" Or resultMagic = "" Then
        MsgBox "合成失败！无法根据规则找到对应的结果。", vbCritical
        GoTo Cleanup
    End If
    
    Call ConsumeMaterial(wsSim.Range("MagicPool"), material1)
    Call ConsumeMaterial(wsSim.Range("MagicPool"), material2)
    
    Call AddMagicToPoolFromResult(resultMagic)
    
    wsSim.Range("B13, D13").ClearContents
    wsSim.Range("F13").Value = resultMagic
    Call TierSelectionChanged
    
    MsgBox "合成成功！新的魔法 '" & resultMagic & "' 已加入你的魔法池。", vbInformation
Cleanup:
    Application.ScreenUpdating = True
    Exit Sub
ErrorHandler:
    MsgBox "合成时发生错误: " & Err.Description, vbCritical
    Resume Cleanup
End Sub

' --- 其余函数保持不变 ---
Public Sub TierSelectionChanged()
    On Error GoTo ErrorHandler
    Application.EnableEvents = False
    Application.ScreenUpdating = False
    Dim wsSim As Worksheet
    Set wsSim = ThisWorkbook.ActiveSheet
    
    Dim tierIndex As Integer
    tierIndex = wsSim.Shapes("ddlTierSelect").ControlFormat.Value
    
    If tierIndex = 0 Then GoTo Cleanup
    
    Dim sourceTierNum As Integer
    sourceTierNum = wsSim.Range("TierSourceNumList").Cells(tierIndex, 1).Value
    
    Call UpdateMaterialDropdowns(wsSim, sourceTierNum)
Cleanup:
    Application.EnableEvents = True
    Application.ScreenUpdating = True
    Exit Sub
ErrorHandler:
    MsgBox "更改阶级时出错: " & Err.Description, vbCritical
    Resume Cleanup
End Sub

Private Sub UpdateMaterialDropdowns(wsSim As Worksheet, sourceTierNum As Integer)
    Dim wsRules As Worksheet: Set wsRules = ThisWorkbook.Sheets("魔法合成逻辑_v4_Pure_Fix")
    
    ' 确保工作表未被保护
    If wsSim.ProtectContents Then
        wsSim.Unprotect
    End If
    
    Dim tagsStr As String: tagsStr = CStr(wsRules.Cells(4 + sourceTierNum, 3).Value)
    Dim tags As Variant: tags = Split(tagsStr, ", ")
    
    Dim ownedMagic As Collection: Set ownedMagic = New Collection
    Dim cell As Range
    On Error Resume Next
    Dim constantsRange As Range
    Set constantsRange = wsSim.Range("MagicPool").SpecialCells(xlCellTypeConstants)
    On Error GoTo 0
    If constantsRange Is Nothing Then GoTo WriteDropdowns
    
    For Each cell In constantsRange
        Dim tag As Variant
        For Each tag In tags
            If InStr(1, CStr(cell.Value), Trim(CStr(tag)), vbTextCompare) = 1 Then
                ownedMagic.Add CStr(cell.Value)
                Exit For
            End If
        Next tag
    Next cell
    
WriteDropdowns:
    wsSim.Range("MaterialList").ClearContents
    If ownedMagic.Count > 0 Then
        Dim i As Integer
        For i = 1 To ownedMagic.Count
            wsSim.Range("MaterialList").Cells(i, 1).Value = ownedMagic(i)
        Next i
    End If
    
    wsSim.Range("B13, D13, F13").ClearContents
    Dim formulaStr As String
    If ownedMagic.Count > 0 Then
        formulaStr = "=OFFSET(MaterialList,0,0," & ownedMagic.Count & ")"
    Else
        formulaStr = "=" & wsSim.Range("Z1").Address
    End If
    
    With wsSim.Range("B13").Validation
        .Delete
        .Add Type:=xlValidateList, Formula1:=formulaStr
    End With
    With wsSim.Range("D13").Validation
        .Delete
        .Add Type:=xlValidateList, Formula1:=formulaStr
    End With
End Sub

' --- 其余辅助函数保持不变 ---
Private Function CalculateResult(wsSim As Worksheet, material1 As String, material2 As String) As String
    ' 保持原样...
    Dim wsRules As Worksheet: Set wsRules = ThisWorkbook.Sheets("魔法合成逻辑_v4_Pure_Fix")
    
    ' -- 移除对UI阶级选择的依赖，直接从魔法名推断标签 --
    Dim tag1 As String, tag2 As String
    tag1 = GetTagFromMagic_Standalone(material1, wsRules)
    tag2 = GetTagFromMagic_Standalone(material2, wsRules)
    
    If tag1 = "" Or tag2 = "" Then
        CalculateResult = "Error"
        Exit Function
    End If
    
    ' 新增规则：相同标签不可合成
    If tag1 = tag2 Then
        CalculateResult = "Error"
        Exit Function
    End If
    
    ' -- 新的、更直接的规则查找逻辑 --
    Dim resultTag As String
    Dim key1 As String: key1 = tag1 & " + " & tag2
    Dim key2 As String: key2 = tag2 & " + " & tag1
    Dim findRange As Range
    
    ' 在规则表的第一列中查找匹配的公式
    Set findRange = wsRules.Columns("A").Find(key1, LookIn:=xlValues, LookAt:=xlWhole)
    If findRange Is Nothing Then
        Set findRange = wsRules.Columns("A").Find(key2, LookIn:=xlValues, LookAt:=xlWhole)
    End If
    
    If Not findRange Is Nothing Then
        ' 如果找到，则结果在右边一列
        resultTag = CStr(findRange.Offset(0, 1).Value)
    Else
        resultTag = ""
    End If
    
    If resultTag <> "" Then
        CalculateResult = resultTag & "1"
    Else
        CalculateResult = "Error"
    End If
End Function

' --- 独立的、更健壮的标签获取函数 ---
Private Function GetTagFromMagic_Standalone(magicName As String, wsRules As Worksheet) As String
    Dim i As Integer
    For i = 1 To 5 ' 遍历所有可能的阶级
        Dim tags As Variant: tags = Split(CStr(wsRules.Cells(4 + i, 3).Value), ", ")
        Dim tag As Variant
        For Each tag In tags
            If InStr(1, magicName, Trim(CStr(tag)), vbTextCompare) = 1 Then
                GetTagFromMagic_Standalone = Trim(CStr(tag))
                Exit Function
            End If
        Next tag
    Next i
    GetTagFromMagic_Standalone = ""
End Function

Private Sub ConsumeMaterial(poolRange As Range, materialName As String)
    Dim cell As Range
    For Each cell In poolRange
        If CStr(cell.Value) = materialName Then
            cell.ClearContents
            Exit For
        End If
    Next cell
End Sub

Private Sub AddMagicToPoolFromResult(magicName As String)
    If magicName = "" Or magicName = "Error1" Then Exit Sub
    Dim wsSim As Worksheet: Set wsSim = ThisWorkbook.ActiveSheet
    
    ' 确保工作表未被保护
    If wsSim.ProtectContents Then
        wsSim.Unprotect
    End If
    
    Dim firstEmptyCell As Range
    On Error Resume Next
    Set firstEmptyCell = wsSim.Range("MagicPool").SpecialCells(xlCellTypeBlanks).Cells(1)
    On Error GoTo 0
    If firstEmptyCell Is Nothing Then
        MsgBox "魔法池已满，无法添加新的魔法！", vbExclamation
    Else
        firstEmptyCell.Value = magicName
    End If
End Sub

Private Function SheetExists(sheetName As String) As Boolean
    Dim ws As Worksheet
    On Error Resume Next
    Set ws = ThisWorkbook.Sheets(sheetName)
    On Error GoTo 0
    SheetExists = Not ws Is Nothing
End Function

' --- 新增功能：模拟玩家随机合成 ---
Public Sub SimulatePlayerRandomSynthesis()
    On Error GoTo ErrorHandler
    Application.ScreenUpdating = False
    
    Dim wsSim As Worksheet
    Set wsSim = ThisWorkbook.ActiveSheet
    
    ' 确保工作表未被保护
    If wsSim.ProtectContents Then wsSim.Unprotect
    
    Dim magicByTier(1 To 5) As Collection
    Dim i As Integer
    For i = 1 To 5
        Set magicByTier(i) = New Collection
    Next i
    
    Dim wsRules As Worksheet
    Set wsRules = ThisWorkbook.Sheets("魔法合成逻辑_v4_Pure_Fix")
    
    ' 1. 扫描魔法池并按阶级分组
    Dim cell As Range
    Dim constantsRange As Range
    On Error Resume Next
    Set constantsRange = wsSim.Range("MagicPool").SpecialCells(xlCellTypeConstants)
    On Error GoTo 0
    
    If constantsRange Is Nothing Then
        MsgBox "魔法池是空的，无法进行随机合成。", vbInformation
        GoTo Cleanup
    End If
    
    For Each cell In constantsRange
        If Trim(CStr(cell.Value)) <> "" Then
            Dim tierNum As Integer
            tierNum = GetTierFromMagic(CStr(cell.Value), wsRules)
            If tierNum > 0 Then
                magicByTier(tierNum).Add CStr(cell.Value)
            End If
        End If
    Next cell
    
    ' 2. 生成所有可行的合成组合列表
    Dim possibleCombos As New Collection
    Dim sourceTier As Integer
    Dim mat1 As String, mat2 As String
    Dim j As Integer, k As Integer
    
    For sourceTier = 1 To 5
        If magicByTier(sourceTier).Count >= 2 Then
            For j = 1 To magicByTier(sourceTier).Count
                For k = j + 1 To magicByTier(sourceTier).Count
                    mat1 = magicByTier(sourceTier)(j)
                    mat2 = magicByTier(sourceTier)(k)
                    
                    ' 检查此组合是否有效 (标签不同)
                    Dim tag1 As String, tag2 As String
                    tag1 = GetTagFromMagic_Standalone(mat1, wsRules)
                    tag2 = GetTagFromMagic_Standalone(mat2, wsRules)
                    
                    If tag1 <> "" And tag2 <> "" And tag1 <> tag2 Then
                        possibleCombos.Add Array(mat1, mat2, sourceTier)
                    End If
                Next k
            Next j
        End If
    Next sourceTier
    
    If possibleCombos.Count = 0 Then
        MsgBox "根据当前规则，没有可执行的合成。", vbInformation
        GoTo Cleanup
    End If
    
    ' 3. 从可行组合中随机选择一个
    Randomize
    Dim chosenCombo As Variant
    chosenCombo = possibleCombos(Int((possibleCombos.Count * Rnd) + 1))
    Dim material1 As String: material1 = chosenCombo(0)
    Dim material2 As String: material2 = chosenCombo(1)
    sourceTier = chosenCombo(2)
    
    ' 4. 调用核心合成逻辑
    wsSim.Range("B13").Value = material1
    wsSim.Range("D13").Value = material2
    
    ' 设置阶级下拉框以匹配合成
    Dim ddlTier As Shape
    Set ddlTier = wsSim.Shapes("ddlTierSelect")
    ddlTier.ControlFormat.Value = sourceTier
    
    ' 短暂地允许事件以触发TierSelectionChanged来更新UI
    Application.EnableEvents = True
    Call TierSelectionChanged
    Application.EnableEvents = False
    
    ' -- 直接执行合成的核心逻辑，而不是调用整个ExecuteSynthesis --
    Dim resultMagic As String
    resultMagic = CalculateResult(wsSim, material1, material2)
    
    If resultMagic = "Error" Or resultMagic = "" Then
        MsgBox "自动模拟合成失败！" & vbCrLf & _
               "无法为材料 " & material1 & " + " & material2 & " 找到合成规则。", vbCritical
    Else
        ' 核心逻辑：消耗材料并添加结果
        Call ConsumeMaterial(wsSim.Range("MagicPool"), material1)
        Call ConsumeMaterial(wsSim.Range("MagicPool"), material2)
        Call AddMagicToPoolFromResult(resultMagic)
        
        ' 更新UI
        wsSim.Range("B13, D13").ClearContents
        wsSim.Range("F13").Value = resultMagic
        Call TierSelectionChanged ' 刷新下拉列表
        
        MsgBox "自动模拟合成完毕！" & vbCrLf & _
               "材料: " & material1 & " + " & material2 & vbCrLf & _
               "结果: " & resultMagic, vbInformation
    End If

Cleanup:
    Application.ScreenUpdating = True
    Application.EnableEvents = True
    Exit Sub
    
ErrorHandler:
    MsgBox "模拟随机合成时发生错误: " & Err.Description, vbCritical
    Resume Cleanup
End Sub

Private Function GetTierFromMagic(magicName As String, wsRules As Worksheet) As Integer
    On Error Resume Next
    Dim i As Integer
    For i = 1 To 5
        Dim tags As Variant
        tags = Split(CStr(wsRules.Cells(4 + i, 3).Value), ", ")
        Dim tag As Variant
        For Each tag In tags
            If InStr(1, magicName, Trim(CStr(tag)), vbTextCompare) = 1 Then
                GetTierFromMagic = i
                Exit Function
            End If
        Next tag
    Next i
    GetTierFromMagic = 0
End Function
Private Function GetTierFromMagic(magicName As String, wsRules As Worksheet) As Integer
    On Error Resume Next
    Dim i As Integer
    For i = 1 To 5
        Dim tags As Variant
        tags = Split(CStr(wsRules.Cells(4 + i, 3).Value), ", ")
        Dim tag As Variant
        For Each tag In tags
            If InStr(1, magicName, Trim(CStr(tag)), vbTextCompare) = 1 Then
                GetTierFromMagic = i
                Exit Function
            End If
        Next tag
    Next i
    GetTierFromMagic = 0
End Function